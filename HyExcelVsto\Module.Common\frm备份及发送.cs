using Common.Utility;
using ET;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Excel文件备份及发送窗体
    /// </summary>
    /// <remarks>
    /// 此窗体提供以下功能：
    /// 1. 生成发送存档：创建带有日期、时间、接收者信息的文件副本
    /// 2. 存档备份：创建带有时间戳和说明的备份文件
    /// 3. 临时文件存档：创建临时文件夹中的文件副本，用于临时存储
    /// 4. 文件路径管理：复制、打开目录等操作
    /// </remarks>
    public partial class frm备份及发送 : Form
    {
        /// <summary>
        /// 初始化窗体
        /// </summary>
        public frm备份及发送()
        {
            InitializeComponent();
            Text = "存档及发送";
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void frmBackAndSend_Load(object sender, EventArgs e)
        {
            try
            {
                textBox存档路径.Text = GlobalSettings.AutoBackupPath;
                ETForm.BindWindowsFormControl(textBox存档路径, ThisAddIn.ConfigurationSettings, "file", "backupfolder");
                ETForm.BindComboBox(comboBox接收者);

                Microsoft.Office.Interop.Excel.Workbook activeWorkbook = ThisAddIn.ExcelApplication.ActiveWorkbook;
                if (activeWorkbook != null)
                {
                    txt发送文件名.Text = FileOperate.GetFileNameNoExtension(activeWorkbook.Name);
                }
                else
                {
                    throw new ETException("没有打开的工作簿", "工作簿操作");
                }
            }
            catch (Exception ex)
            {
                throw new ETException("窗体加载失败", "界面操作", ex);
            }
        }

        /// <summary>
        /// 生成发送存档按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void button生成发送存档_Click(object sender, EventArgs e)
        {
            try
            {
                string outputPath = 生成发送存档(
                    fileName: txt发送文件名.Text,
                    includeDate: chk日期.Checked,
                    includeTime: chk时间.Checked,
                    recipient: comboBox接收者.Text,
                    description: textBox说明.Text
                );

                textBox输出路径.Text = outputPath;
                ETFile.FileCopyToClipboard(outputPath);
                autoResetLabel提示1.Text = "已复制文件路径到剪贴板";
                ETLogManager.Info($"成功生成发送存档：{outputPath}");
            }
            catch (Exception ex)
            {
                throw new ETException("生成发送存档失败", "文件操作", ex);
            }
        }

        /// <summary>
        /// 存档备份按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void button存档备份_Click(object sender, EventArgs e)
        {
            try
            {
                Microsoft.Office.Interop.Excel.Workbook activeWorkbook = ThisAddIn.ExcelApplication.ActiveWorkbook;
                if (activeWorkbook == null)
                {
                    throw new ETException("没有打开的工作簿", "工作簿操作");
                }

                string outputPath = 生成发送存档(
                    fileName: FileOperate.GetFileNameNoExtension(activeWorkbook.Name),
                    includeDate: true,
                    includeTime: true,
                    recipient: string.Empty,
                    description: textBox加备注.Text,
                    backupFolder: "手动备份存档"
                );

                textBox备份存档路径.Text = outputPath;
                ETFile.FileCopyToClipboard(outputPath);
                autoResetLabel提示2.Text = "已复制文件路径到剪贴板";
                ETLogManager.Info($"成功生成备份存档：{outputPath}");
            }
            catch (Exception ex)
            {
                throw new ETException("存档备份失败", "文件操作", ex);
            }
        }

        /// <summary>
        /// 临时文件存档按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void button临时文件存档_Click(object sender, EventArgs e)
        {
            try
            {
                Microsoft.Office.Interop.Excel.Workbook activeWorkbook = ThisAddIn.ExcelApplication.ActiveWorkbook;
                if (activeWorkbook == null)
                {
                    throw new ETException("没有打开的工作簿", "工作簿操作");
                }

                string outputPath = 生成发送存档(
                    fileName: FileOperate.GetFileNameNoExtension(activeWorkbook.Name),
                    includeDate: true,
                    includeTime: true,
                    recipient: string.Empty,
                    description: textBox临时文件备注.Text,
                    backupFolder: "临时存档"
                );

                textBox临时文件路径.Text = outputPath;
                ETFile.FileCopyToClipboard(outputPath);
                autoResetLabel提示3.Text = "已复制文件路径到剪贴板";
                ETLogManager.Info($"成功生成临时文件存档：{outputPath}");
            }
            catch (Exception ex)
            {
                throw new ETException("临时文件存档失败", "文件操作", ex);
            }
        }

        /// <summary>
        /// 生成发送存档文件
        /// </summary>
        /// <param name="fileName">文件名（不含扩展名）</param>
        /// <param name="includeDate">是否包含日期</param>
        /// <param name="includeTime">是否包含时间</param>
        /// <param name="recipient">接收者</param>
        /// <param name="description">说明文本</param>
        /// <param name="backupFolder">备份文件夹名称，默认为"发送存档"</param>
        /// <returns>生成的文件完整路径</returns>
        /// <exception cref="HyException">当文件操作失败时抛出</exception>
        private string 生成发送存档(string fileName, bool includeDate, bool includeTime,
            string recipient, string description, string backupFolder = "发送存档")
        {
            try
            {
                // 生成文件名
                string sanitizedFileName = fileName.Trim().PathRemoveInvalidChars("_");
                string baseFileName = string.IsNullOrEmpty(sanitizedFileName)
                    ? ThisAddIn.ExcelApplication.ActiveWorkbook.Name
                    : sanitizedFileName;

                // 获取原始工作簿的扩展名和文件名
                string originalFile = ThisAddIn.ExcelApplication.ActiveWorkbook.Name;
                string fileExtension = originalFile.Contains(".")
                    ? originalFile.Substring(originalFile.LastIndexOf('.'))
                    : ".xlsx";

                // 如果用户输入的文件名包含扩展名，则移除
                string fileNameWithoutExt = baseFileName;
                if (baseFileName.EndsWith(fileExtension, StringComparison.OrdinalIgnoreCase))
                {
                    fileNameWithoutExt = baseFileName.Substring(0, baseFileName.Length - fileExtension.Length);
                }

                // 构建文件名部分
                List<string> fileNameParts = [fileNameWithoutExt];
                DateTime now = DateTime.Now;
                if (includeDate) fileNameParts.Add(now.ToString("yyyyMMdd"));
                if (includeTime) fileNameParts.Add(now.ToString("HHmmss"));

                // 创建目标路径
                string basePath = Path.Combine(GlobalSettings.AutoBackupPath, backupFolder, now.ToString("yyyy年"));
                if (!string.IsNullOrWhiteSpace(recipient))
                {
                    basePath = Path.Combine(basePath, recipient);
                }

                string subDir = Path.Combine(basePath, $"{now:yyyyMMdd-HHmmss}-{fileNameWithoutExt}");
                Directory.CreateDirectory(subDir);

                string fullFileName = $"{string.Join("-", fileNameParts)}{fileExtension}";
                string filePath = Path.Combine(subDir, fullFileName);

                // 保存副本到指定路径
                ETExcelExtensions.SaveCopyAs(ThisAddIn.ExcelApplication.ActiveWorkbook, filePath);

                // 将文件添加至锁定文件管理，并设置为只读
                ThisAddIn.AddToLockFile(filePath);
                File.SetAttributes(filePath, File.GetAttributes(filePath) | FileAttributes.ReadOnly);

                // 生成说明文件
                if (!string.IsNullOrWhiteSpace(description))
                {
                    string descriptionFileName = $"{Path.GetFileNameWithoutExtension(fullFileName)}_说明.txt";
                    string descriptionFilePath = Path.Combine(subDir, descriptionFileName);
                    File.WriteAllText(descriptionFilePath, description, Encoding.UTF8);
                    File.SetAttributes(descriptionFilePath, FileAttributes.ReadOnly);
                }

                return filePath;
            }
            catch (Exception ex)
            {
                throw new ETException("生成存档文件失败", "文件操作", ex);
            }
        }

        /// <summary>
        /// 输出路径双击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void textBox输出路径_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                string filePath = ((TextBox)sender).Text.Trim();
                if (File.Exists(filePath))
                {
                    ETFile.FileCopyToClipboard(filePath);
                    ETLogManager.Info($"已复制文件路径到剪贴板：{filePath}");
                }
                else
                {
                    MessageBox.Show(@"文件不存在");
                    ETLogManager.Error("尝试复制不存在的文件路径", new FileNotFoundException($"文件不存在：{filePath}"));
                }
            }
            catch (Exception ex)
            {
                throw new ETException("复制文件路径失败", "文件操作", ex);
            }
        }

        /// <summary>
        /// 复制路径菜单项点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void 复制路径ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(textBox输出路径.Text)) return;
            Clipboard.SetText(textBox输出路径.Text);
            ETLogManager.Info($"已复制路径到剪贴板：{textBox输出路径.Text}");
        }

        /// <summary>
        /// 打开所在目录菜单项点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void 打开所在目录ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                FileInfo fileInfo = new(textBox输出路径.Text);
                if (fileInfo.DirectoryName != null)
                {
                    Process.Start(fileInfo.DirectoryName);
                    ETLogManager.Info($"已打开目录：{fileInfo.DirectoryName}");
                }
            }
            catch (Exception ex)
            {
                throw new ETException("打开目录失败", "文件操作", ex);
            }
        }

        /// <summary>
        /// 复制文件菜单项点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void 复制文件ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            string filePath = textBox输出路径.Text;
            if (File.Exists(filePath))
            {
                ETFile.FileCopyToClipboard(filePath);
                ETLogManager.Info($"已复制文件到剪贴板：{filePath}");
            }
            else
            {
                MessageBox.Show(@"文件不存在");
                ETLogManager.Error("尝试复制不存在的文件", new FileNotFoundException($"文件不存在：{filePath}"));
            }
        }
    }
}