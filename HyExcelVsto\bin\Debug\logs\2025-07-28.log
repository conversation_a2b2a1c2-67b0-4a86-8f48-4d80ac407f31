﻿2025-07-28 09:37:33 [INFO] Excel窗口句柄监控器初始化完成
2025-07-28 09:37:34 [INFO] 配置文件实例已在加载时初始化
2025-07-28 09:37:34 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-07-28 09:37:34 [INFO] 🔍 === 直接测试znAbout控件状态 ===
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件实例: 存在
2025-07-28 09:37:34 [INFO] 🔍 znAbout.Label: 'ZnAbout'
2025-07-28 09:37:34 [INFO] 🔍 znAbout.Name: 'znAbout'
2025-07-28 09:37:34 [INFO] 🔍 znAbout类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-28 09:37:34 [INFO] 🔍 znAboutGroup控件实例: 存在
2025-07-28 09:37:34 [INFO] 🔍 znAboutGroup.Label: '授权'
2025-07-28 09:37:34 [INFO] 🔍 znAboutGroup.Name: 'znAboutGroup'
2025-07-28 09:37:34 [INFO] 🔍 znAboutGroup类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-28 09:37:34 [INFO] 🔍 znAboutButton控件实例: 存在
2025-07-28 09:37:34 [INFO] 🔍 znAboutButton.Label: '授权'
2025-07-28 09:37:34 [INFO] 🔍 znAboutButton.Name: 'znAboutButton'
2025-07-28 09:37:34 [INFO] 🔍 znAboutButton类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-28 09:37:34 [INFO] 🔍 === znAbout控件状态测试完成 ===
2025-07-28 09:37:34 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-07-28 09:37:34 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-07-28 09:37:34 [INFO] 成功初始化Excel应用程序实例
2025-07-28 09:37:34 [INFO] 自动备份路径未配置
2025-07-28 09:37:34 [DEBUG] 开始初始化授权控制器
2025-07-28 09:37:34 [DEBUG] 授权系统初始化完成，耗时: 312ms
2025-07-28 09:37:34 [DEBUG] 开始初始化授权验证
2025-07-28 09:37:34 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-07-28 09:37:34 [DEBUG] 权限管理器初始化成功
2025-07-28 09:37:34 [DEBUG] 使用新的权限管理器进行初始化
2025-07-28 09:37:34 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-28 09:37:34 [INFO] 开始初始化UI权限管理
2025-07-28 09:37:34 [DEBUG] [实例ID: f60db69d] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-28 09:37:34 [DEBUG] 🔍 [实例ID: f60db69d] 字典引用一致性检查:
2025-07-28 09:37:34 [DEBUG] 🔍   标题映射一致性: True
2025-07-28 09:37:34 [DEBUG] 🔍   权限映射一致性: True
2025-07-28 09:37:34 [DEBUG] 🔍   信息映射一致性: True
2025-07-28 09:37:34 [DEBUG] 🔍   特殊控件一致性: True
2025-07-28 09:37:34 [DEBUG] 控件权限管理器初始化完成 [实例ID: f60db69d]
2025-07-28 09:37:34 [DEBUG] 开始注册控件权限映射
2025-07-28 09:37:34 [INFO] 开始初始化全局控件映射
2025-07-28 09:37:34 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-07-28 09:37:34 [DEBUG] 开始生成控件标题映射
2025-07-28 09:37:34 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-28 09:37:34 [DEBUG] 通过反射获取到 112 个字段
2025-07-28 09:37:34 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-28 09:37:34 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-28 09:37:34 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-28 09:37:34 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-28 09:37:34 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-28 09:37:34 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-28 09:37:34 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-28 09:37:34 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-07-28 09:37:34 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-07-28 09:37:34 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-07-28 09:37:34 [INFO] 控件标题映射生成完成，共生成 100 项映射
2025-07-28 09:37:34 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-28 09:37:34 [DEBUG] 全局控件标题映射生成完成，共生成 100 项
2025-07-28 09:37:34 [INFO] 关键控件标题映射: hyTab -> Develop
2025-07-28 09:37:34 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-07-28 09:37:34 [WARN] 关键控件未找到标题映射: buttonAbout
2025-07-28 09:37:34 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-07-28 09:37:34 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-07-28 09:37:34 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-07-28 09:37:34 [INFO] === znAbout控件标题映射诊断 ===
2025-07-28 09:37:34 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-07-28 09:37:34 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-07-28 09:37:34 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-07-28 09:37:34 [DEBUG] === 所有生成的控件标题映射 ===
2025-07-28 09:37:34 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-07-28 09:37:34 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-07-28 09:37:34 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-07-28 09:37:34 [DEBUG] 控件映射: btn发送及存档 -> '发送及存档'
2025-07-28 09:37:34 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-07-28 09:37:34 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-07-28 09:37:34 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-07-28 09:37:34 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-07-28 09:37:34 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-07-28 09:37:34 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规检查'
2025-07-28 09:37:34 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-07-28 09:37:34 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-07-28 09:37:34 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-07-28 09:37:34 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-07-28 09:37:34 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-07-28 09:37:34 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-07-28 09:37:34 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-07-28 09:37:34 [DEBUG] 控件映射: button14 -> '发送及存档'
2025-07-28 09:37:34 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-07-28 09:37:34 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-07-28 09:37:34 [DEBUG] 控件映射: button17 -> '向下填充'
2025-07-28 09:37:34 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-07-28 09:37:34 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-07-28 09:37:34 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-07-28 09:37:34 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-07-28 09:37:34 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-07-28 09:37:34 [DEBUG] 控件映射: button3 -> '关于'
2025-07-28 09:37:34 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-07-28 09:37:34 [DEBUG] 控件映射: button5 -> '文件管理'
2025-07-28 09:37:34 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-07-28 09:37:34 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-07-28 09:37:34 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-07-28 09:37:34 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-07-28 09:37:34 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-07-28 09:37:34 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-07-28 09:37:34 [DEBUG] 控件映射: button9 -> '文件快开'
2025-07-28 09:37:34 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-07-28 09:37:34 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-07-28 09:37:34 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-07-28 09:37:34 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-07-28 09:37:34 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-07-28 09:37:34 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-07-28 09:37:34 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-07-28 09:37:34 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-07-28 09:37:34 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-07-28 09:37:34 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-07-28 09:37:34 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-07-28 09:37:34 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-07-28 09:37:34 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-07-28 09:37:34 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-07-28 09:37:34 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-07-28 09:37:34 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-07-28 09:37:34 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-07-28 09:37:34 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-07-28 09:37:34 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-07-28 09:37:34 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-07-28 09:37:34 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-07-28 09:37:34 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-07-28 09:37:34 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-07-28 09:37:34 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-07-28 09:37:34 [DEBUG] 控件映射: button文件操作 -> '文件操作'
2025-07-28 09:37:34 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-07-28 09:37:34 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-07-28 09:37:34 [DEBUG] 控件映射: button专用工具 -> '专用工具'
2025-07-28 09:37:34 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-07-28 09:37:34 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-07-28 09:37:34 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-07-28 09:37:34 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-07-28 09:37:34 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-07-28 09:37:34 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-07-28 09:37:34 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-07-28 09:37:34 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-07-28 09:37:34 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-07-28 09:37:34 [DEBUG] 控件映射: group1 -> '关于'
2025-07-28 09:37:34 [DEBUG] 控件映射: group2 -> '脚本'
2025-07-28 09:37:34 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-07-28 09:37:34 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-07-28 09:37:34 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-07-28 09:37:34 [DEBUG] 控件映射: group文件 -> '文件'
2025-07-28 09:37:34 [DEBUG] 控件映射: group无线 -> '无线'
2025-07-28 09:37:34 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-07-28 09:37:34 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-07-28 09:37:34 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-07-28 09:37:34 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-07-28 09:37:34 [DEBUG] 控件映射: menu1 -> '其它'
2025-07-28 09:37:34 [DEBUG] 控件映射: menu3 -> '设置'
2025-07-28 09:37:34 [DEBUG] 控件映射: menu5 -> '修复'
2025-07-28 09:37:34 [DEBUG] 控件映射: menuHY -> '其它'
2025-07-28 09:37:34 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-07-28 09:37:34 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-07-28 09:37:34 [DEBUG] 控件映射: menu修复 -> '修复'
2025-07-28 09:37:34 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-07-28 09:37:34 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-07-28 09:37:34 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-07-28 09:37:34 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-07-28 09:37:34 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-07-28 09:37:34 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-07-28 09:37:34 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-07-28 09:37:34 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-07-28 09:37:34 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-07-28 09:37:34 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-07-28 09:37:34 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-07-28 09:37:34 [DEBUG] 开始生成控件权限映射
2025-07-28 09:37:34 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-28 09:37:34 [DEBUG] 通过反射获取到 112 个字段
2025-07-28 09:37:34 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 09:37:34 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-28 09:37:34 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-28 09:37:34 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-28 09:37:34 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-28 09:37:34 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-28 09:37:34 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-28 09:37:34 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 09:37:34 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-28 09:37:34 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-28 09:37:34 [INFO] 控件权限映射生成完成，共生成 104 项映射
2025-07-28 09:37:34 [DEBUG] 全局控件权限映射生成完成，共生成 104 项
2025-07-28 09:37:34 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-07-28 09:37:34 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-07-28 09:37:34 [INFO] 全局控件映射初始化完成 - 标题映射: 100 项, 权限映射: 104 项
2025-07-28 09:37:34 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-28 09:37:34 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-28 09:37:34 [INFO] 开始初始化权限验证
2025-07-28 09:37:34 [DEBUG] 设置默认UI可见性为false
2025-07-28 09:37:34 [DEBUG] 开始检查所有需要的权限
2025-07-28 09:37:34 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-28 09:37:34 [INFO] 启动网络授权信息获取任务
2025-07-28 09:37:34 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-07-28 09:37:34 [INFO] 所有权限检查完成
2025-07-28 09:37:34 [DEBUG] 应用权限状态到UI控件
2025-07-28 09:37:34 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 09:37:34 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 09:37:34 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 09:37:34 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 09:37:34 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 09:37:34 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 09:37:34 [DEBUG] 已应用权限状态到UI控件
2025-07-28 09:37:34 [DEBUG] 启动后台权限刷新任务
2025-07-28 09:37:34 [DEBUG] 启动延迟权限刷新任务
2025-07-28 09:37:34 [INFO] 权限验证初始化完成
2025-07-28 09:37:34 [INFO] UI权限管理初始化完成
2025-07-28 09:37:34 [INFO] 收到权限管理器初始化完成通知
2025-07-28 09:37:34 [INFO] 开始刷新控件标题
2025-07-28 09:37:34 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 09:37:34 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 09:37:34 [DEBUG] 控件标题刷新完成
2025-07-28 09:37:34 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 09:37:34 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 09:37:34 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 09:37:34 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 09:37:34 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 09:37:34 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 09:37:34 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 09:37:34 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 09:37:34 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 09:37:34 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 09:37:34 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 09:37:34 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 09:37:34 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 09:37:34 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 09:37:34 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 09:37:34 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 09:37:34 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 09:37:34 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 09:37:34 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 09:37:34 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 09:37:34 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 09:37:34 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 09:37:34 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 09:37:34 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 09:37:34 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 09:37:34 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 09:37:34 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 09:37:34 [INFO] 控件标题更正完成
2025-07-28 09:37:34 [INFO] 控件标题刷新完成
2025-07-28 09:37:34 [INFO] 权限管理器初始化完成处理结束
2025-07-28 09:37:34 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-28 09:37:34 [DEBUG] 授权验证初始化完成
2025-07-28 09:37:34 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-07-28 09:37:34 [INFO] 成功加载配置和授权信息
2025-07-28 09:37:34 [INFO] 开始初始化定时器和设置
2025-07-28 09:37:34 [INFO] 定时器和设置初始化完成
2025-07-28 09:37:34 [INFO] 开始VSTO插件启动流程
2025-07-28 09:37:34 [INFO] TopMostForm窗体加载完成
2025-07-28 09:37:34 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 09:37:34 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 2691012
2025-07-28 09:37:34 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 2691012)
2025-07-28 09:37:34 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 2691012
2025-07-28 09:37:34 [INFO] 系统事件监控已启动
2025-07-28 09:37:34 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 09:37:34 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-07-28 09:37:34 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-07-28 09:37:34 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-07-28 09:37:34 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 2234136
2025-07-28 09:37:34 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-07-28 09:37:34 [INFO] VSTO插件启动流程完成
2025-07-28 09:37:35 [INFO] 从Remote成功获取到网络授权信息
2025-07-28 09:37:35 [INFO] 网络授权信息已更新并触发回调
2025-07-28 09:37:35 [INFO] 网络授权信息已从 Network 更新
2025-07-28 09:37:35 [INFO] 授权版本: 1.0
2025-07-28 09:37:35 [INFO] 颁发者: ExtensionsTools
2025-07-28 09:37:35 [INFO] 用户数量: 2
2025-07-28 09:37:35 [INFO] 分组权限数量: 2
2025-07-28 09:37:35 [WARN] 配置文件中未找到用户组信息
2025-07-28 09:37:35 [INFO] 已重新设置用户组: []
2025-07-28 09:37:35 [INFO] 用户组信息已重新设置
2025-07-28 09:37:35 [INFO] 立即刷新权限缓存和UI界面
2025-07-28 09:37:35 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-28 09:37:35 [DEBUG] 使用新的权限管理器进行强制刷新
2025-07-28 09:37:35 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-07-28 09:37:35 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-28 09:37:35 [DEBUG] 本地权限缓存已清空
2025-07-28 09:37:35 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-07-28 09:37:35 [INFO] 所有权限检查完成
2025-07-28 09:37:35 [DEBUG] 权限重新检查完成
2025-07-28 09:37:35 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 09:37:35 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 09:37:35 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 09:37:35 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 09:37:35 [DEBUG] 已应用权限状态到UI控件
2025-07-28 09:37:35 [INFO] UI界面权限状态已更新
2025-07-28 09:37:35 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 09:37:35 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 09:37:35 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-07-28 09:37:35 [INFO] 权限缓存和UI界面立即刷新完成
2025-07-28 09:37:35 [INFO] 网络授权已更新，开始刷新控件标题
2025-07-28 09:37:35 [INFO] 开始刷新Ribbon控件标题
2025-07-28 09:37:35 [DEBUG] 权限缓存已清空，清除了 104 个缓存项
2025-07-28 09:37:35 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-07-28 09:37:35 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 09:37:35 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 09:37:35 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 09:37:35 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 09:37:35 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 09:37:35 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 09:37:35 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 09:37:35 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 09:37:35 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 09:37:35 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 09:37:35 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 09:37:35 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 09:37:35 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 09:37:35 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 09:37:35 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 09:37:35 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 09:37:35 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 09:37:35 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 09:37:35 [INFO] 控件标题更正完成
2025-07-28 09:37:35 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-07-28 09:37:35 [INFO] Ribbon控件标题刷新完成
2025-07-28 09:37:35 [INFO] 控件标题刷新完成
2025-07-28 09:37:35 [DEBUG] Ribbon控件标题已刷新
2025-07-28 09:37:35 [INFO] 开始刷新控件标题
2025-07-28 09:37:35 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 09:37:35 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 09:37:35 [DEBUG] 控件标题刷新完成
2025-07-28 09:37:35 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 09:37:35 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 09:37:35 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 09:37:35 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 09:37:35 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 09:37:35 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 09:37:35 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 09:37:35 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 09:37:35 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 09:37:35 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 09:37:35 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 09:37:35 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 09:37:35 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 09:37:35 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 09:37:35 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 09:37:35 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 09:37:35 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 09:37:35 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 09:37:35 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 09:37:35 [INFO] 控件标题更正完成
2025-07-28 09:37:35 [INFO] 控件标题刷新完成
2025-07-28 09:37:35 [DEBUG] Ribbon控件标题已立即刷新
2025-07-28 09:37:35 [INFO] 开始刷新授权状态
2025-07-28 09:37:35 [DEBUG] 开始初始化授权验证
2025-07-28 09:37:35 [DEBUG] 使用新的权限管理器进行初始化
2025-07-28 09:37:35 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-28 09:37:35 [INFO] 开始初始化UI权限管理
2025-07-28 09:37:35 [DEBUG] [实例ID: b2d19403] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-28 09:37:35 [DEBUG] 🔍 [实例ID: b2d19403] 字典引用一致性检查:
2025-07-28 09:37:35 [DEBUG] 🔍   标题映射一致性: True
2025-07-28 09:37:35 [DEBUG] 🔍   权限映射一致性: True
2025-07-28 09:37:35 [DEBUG] 🔍   信息映射一致性: True
2025-07-28 09:37:35 [DEBUG] 🔍   特殊控件一致性: True
2025-07-28 09:37:35 [DEBUG] 控件权限管理器初始化完成 [实例ID: b2d19403]
2025-07-28 09:37:35 [DEBUG] 开始注册控件权限映射
2025-07-28 09:37:35 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-28 09:37:35 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-28 09:37:35 [INFO] 开始初始化权限验证
2025-07-28 09:37:35 [DEBUG] 设置默认UI可见性为false
2025-07-28 09:37:35 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 09:37:35 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 2691012
2025-07-28 09:37:36 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 2691012)
2025-07-28 09:37:36 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 09:37:36 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 09:37:36 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 09:37:36 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 09:37:36 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 09:37:36 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 09:37:36 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 09:37:36 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 09:37:36 [DEBUG] 已应用权限状态到UI控件
2025-07-28 09:37:36 [DEBUG] 开始重置 208 个命令栏
2025-07-28 09:37:36 [DEBUG] 开始检查所有需要的权限
2025-07-28 09:37:36 [INFO] 所有权限检查完成
2025-07-28 09:37:36 [DEBUG] 应用权限状态到UI控件
2025-07-28 09:37:36 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 09:37:36 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 09:37:36 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 09:37:36 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 09:37:36 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 09:37:36 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 09:37:36 [DEBUG] 已应用权限状态到UI控件
2025-07-28 09:37:36 [DEBUG] 启动后台权限刷新任务
2025-07-28 09:37:36 [DEBUG] 启动延迟权限刷新任务
2025-07-28 09:37:36 [INFO] 权限验证初始化完成
2025-07-28 09:37:36 [INFO] UI权限管理初始化完成
2025-07-28 09:37:36 [INFO] 收到权限管理器初始化完成通知
2025-07-28 09:37:36 [INFO] 开始刷新控件标题
2025-07-28 09:37:36 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 09:37:36 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 09:37:36 [DEBUG] 控件标题刷新完成
2025-07-28 09:37:36 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 09:37:36 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 09:37:36 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 09:37:36 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 09:37:36 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 09:37:36 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 09:37:36 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 09:37:36 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 09:37:36 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 09:37:36 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 09:37:36 [DEBUG] 已应用权限状态到UI控件
2025-07-28 09:37:36 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 09:37:36 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 09:37:36 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 09:37:36 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 09:37:36 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 09:37:36 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 09:37:36 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 09:37:36 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 09:37:36 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 09:37:36 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 09:37:36 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 09:37:36 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 09:37:36 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 09:37:36 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 09:37:36 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 09:37:36 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 09:37:36 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 09:37:36 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 09:37:36 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 09:37:36 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 09:37:36 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 09:37:36 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 09:37:36 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 09:37:36 [INFO] 控件标题更正完成
2025-07-28 09:37:36 [INFO] 控件标题刷新完成
2025-07-28 09:37:36 [INFO] 权限管理器初始化完成处理结束
2025-07-28 09:37:36 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-28 09:37:36 [DEBUG] 授权验证初始化完成
2025-07-28 09:37:36 [INFO] 授权状态刷新完成
2025-07-28 09:37:36 [DEBUG] 重置命令栏: cell
2025-07-28 09:37:36 [DEBUG] 重置命令栏: column
2025-07-28 09:37:36 [DEBUG] 重置命令栏: row
2025-07-28 09:37:36 [DEBUG] 重置命令栏: cell
2025-07-28 09:37:36 [DEBUG] 重置命令栏: column
2025-07-28 09:37:36 [DEBUG] 重置命令栏: row
2025-07-28 09:37:36 [DEBUG] 重置命令栏: row
2025-07-28 09:37:36 [DEBUG] 重置命令栏: column
2025-07-28 09:37:36 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 09:37:36 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 09:37:36 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 09:37:36 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 09:37:36 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 09:37:36 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 09:37:36 [DEBUG] 已应用权限状态到UI控件
2025-07-28 09:37:37 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-07-28 09:37:38 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 09:37:38 [DEBUG] 授权控制器已初始化
2025-07-28 09:37:38 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 09:37:38 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 09:37:38 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 09:37:38 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 09:37:38 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 09:37:38 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 09:37:38 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 09:37:38 [DEBUG] 已应用权限状态到UI控件
2025-07-28 09:37:38 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 09:37:39 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 09:37:39 [DEBUG] 授权控制器已初始化
2025-07-28 09:37:39 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 09:37:39 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 09:37:40 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 09:37:40 [DEBUG] 授权控制器已初始化
2025-07-28 09:37:40 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 09:37:41 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 09:37:41 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 09:37:41 [DEBUG] 授权控制器已初始化
2025-07-28 09:37:41 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 09:37:42 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 09:37:43 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 09:37:43 [DEBUG] 授权控制器已初始化
2025-07-28 09:37:43 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 09:37:44 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 09:37:44 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 09:37:44 [DEBUG] 授权控制器已初始化
2025-07-28 09:37:44 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 09:37:45 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 09:37:45 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 09:37:45 [DEBUG] 授权控制器已初始化
2025-07-28 09:37:45 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 09:37:46 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 09:37:46 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 09:37:46 [DEBUG] 授权控制器已初始化
2025-07-28 09:37:46 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 09:37:46 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 09:37:47 [DEBUG] 已重置工作表标签菜单
2025-07-28 09:37:47 [DEBUG] 工作表标签菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 09:38:14 [INFO] OpenForm: 准备打开窗体 'ExcelFileManager'，位置: Center，单实例: True
2025-07-28 09:38:14 [INFO] 开始显示窗体 'ExcelFileManager'，位置模式: Center
2025-07-28 09:38:14 [INFO] 窗体 'ExcelFileManager' 以TopMostForm为父窗体显示
2025-07-28 09:38:14 [INFO] 窗体 'ExcelFileManager' 显示完成，句柄: 1776216
2025-07-28 09:38:14 [INFO] OpenForm: 窗体 'ExcelFileManager' 打开成功
2025-07-28 09:38:19 [INFO] App_WorkbookOpen: 工作簿 '★新站站名地址.xlsx' 打开事件触发
2025-07-28 09:38:19 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 09:38:19 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 09:38:19 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 09:38:19 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 09:38:19 [INFO] App_WorkbookOpen: 工作簿 '★新站站名地址.xlsx' 打开处理完成
2025-07-28 09:38:19 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 09:38:19 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4785806)
2025-07-28 09:38:19 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 09:38:19 [INFO] App_WorkbookActivate: 工作簿 '★新站站名地址.xlsx' 激活处理完成
2025-07-28 09:38:19 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 09:38:19 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4785806)
2025-07-28 09:38:19 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 09:38:19 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 09:38:19 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 09:38:19 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 09:38:19 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 09:38:19 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 09:38:19 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 09:38:19 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 09:38:20 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 09:38:20 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 09:38:20 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 09:38:20 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 09:38:20 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 09:38:20 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 09:38:20 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 09:38:20 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-28 09:38:27 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 09:38:27 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 09:38:27 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 09:38:27 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 09:38:27 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 09:41:34 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 09:41:35 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 09:41:35 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 09:41:35 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 09:41:35 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 09:41:54 [INFO] App_WorkbookOpen: 工作簿 '★铁塔会审.xlsx' 打开事件触发
2025-07-28 09:41:54 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 09:41:54 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 4785806, 新父窗口: 4004224
2025-07-28 09:41:54 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4004224)
2025-07-28 09:41:54 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 09:41:54 [INFO] App_WorkbookOpen: 工作簿 '★铁塔会审.xlsx' 打开处理完成
2025-07-28 09:41:54 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 09:41:54 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4004224)
2025-07-28 09:41:54 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 09:41:54 [INFO] App_WorkbookActivate: 工作簿 '★铁塔会审.xlsx' 激活处理完成
2025-07-28 09:41:54 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 09:41:54 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4004224)
2025-07-28 09:41:54 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 09:41:54 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 09:41:54 [WARN] 检测到Excel窗口句柄变化: 4785806 -> 4004224
2025-07-28 09:41:54 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4004224)
2025-07-28 09:41:55 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 09:41:55 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4004224
2025-07-28 09:41:55 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4004224)
2025-07-28 09:41:55 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 09:41:55 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 09:41:55 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 09:41:55 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4004224
2025-07-28 09:41:55 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4004224)
2025-07-28 09:41:55 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 09:41:55 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 09:41:55 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4004224
2025-07-28 09:41:55 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4004224)
2025-07-28 09:41:55 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 09:41:55 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-28 09:42:01 [INFO] OpenForm: 准备打开窗体 '批量查找'，位置: Right，单实例: True
2025-07-28 09:42:01 [INFO] 开始显示窗体 '批量查找'，位置模式: Right
2025-07-28 09:42:02 [INFO] 窗体 '批量查找' 以TopMostForm为父窗体显示
2025-07-28 09:42:02 [INFO] 窗体 '批量查找' 显示完成，句柄: 2426540
2025-07-28 09:42:02 [INFO] OpenForm: 窗体 '批量查找' 打开成功
2025-07-28 09:43:23 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 09:43:23 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 4004224, 新父窗口: 4785806
2025-07-28 09:43:23 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 09:43:23 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 09:43:23 [INFO] App_WorkbookActivate: 工作簿 '★新站站名地址.xlsx' 激活处理完成
2025-07-28 09:43:23 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 09:43:23 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4785806)
2025-07-28 09:43:23 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 09:43:23 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 09:43:24 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 09:43:24 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 09:43:24 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 09:43:24 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 09:43:24 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 09:43:24 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 09:43:24 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 09:43:24 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 09:43:24 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 09:47:59 [INFO] OpenForm: 准备打开窗体 '备份及发送'，位置: Center，单实例: True
2025-07-28 09:47:59 [INFO] 开始显示窗体 '备份及发送'，位置模式: Center
2025-07-28 09:47:59 [INFO] 窗体 '备份及发送' 以TopMostForm为父窗体显示
2025-07-28 09:47:59 [INFO] 窗体 '备份及发送' 显示完成，句柄: 20188514
2025-07-28 09:47:59 [INFO] OpenForm: 窗体 '备份及发送' 打开成功
2025-07-28 09:48:11 [INFO] 成功生成发送存档：E:\资料存放\OfficeAutoBackup\发送存档\2025年\李鑫\20250728-094810-★新站站名地址\★新站站名地址-20250728.xlsx
2025-07-28 09:49:46 [INFO] App_WorkbookOpen: 工作簿 '电信七期20250725.xlsx' 打开事件触发
2025-07-28 09:49:46 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 09:49:46 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 4785806, 新父窗口: 10163446
2025-07-28 09:49:46 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10163446)
2025-07-28 09:49:46 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 09:49:46 [INFO] App_WorkbookOpen: 工作簿 '电信七期20250725.xlsx' 打开处理完成
2025-07-28 09:49:46 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 09:49:46 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10163446)
2025-07-28 09:49:46 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 09:49:46 [INFO] App_WorkbookActivate: 工作簿 '电信七期20250725.xlsx' 激活处理完成
2025-07-28 09:49:46 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 09:49:46 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10163446)
2025-07-28 09:49:46 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 09:49:46 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 09:49:46 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 09:49:46 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 09:49:46 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10163446
2025-07-28 09:49:46 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10163446)
2025-07-28 09:49:46 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 09:49:46 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 09:49:47 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10163446)
2025-07-28 09:49:47 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 09:49:47 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 09:49:47 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10163446
2025-07-28 09:49:47 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10163446)
2025-07-28 09:49:47 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 09:49:47 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-28 09:50:25 [INFO] App_WorkbookOpen: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 打开事件触发
2025-07-28 09:50:25 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 09:50:25 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 10163446, 新父窗口: 3086844
2025-07-28 09:50:25 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3086844)
2025-07-28 09:50:25 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 09:50:25 [INFO] App_WorkbookOpen: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 打开处理完成
2025-07-28 09:50:25 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 09:50:25 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 3086844)
2025-07-28 09:50:25 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 09:50:25 [INFO] App_WorkbookActivate: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 激活处理完成
2025-07-28 09:50:25 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 09:50:25 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 3086844)
2025-07-28 09:50:25 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 09:50:25 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 09:50:26 [WARN] 检测到Excel窗口句柄变化: 10163446 -> 3086844
2025-07-28 09:50:26 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 3086844)
2025-07-28 09:50:26 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 09:50:26 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3086844
2025-07-28 09:50:26 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3086844)
2025-07-28 09:50:26 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 09:50:26 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 09:50:26 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 09:50:26 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3086844
2025-07-28 09:50:26 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3086844)
2025-07-28 09:50:26 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 09:50:26 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 09:50:26 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3086844
2025-07-28 09:50:26 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3086844)
2025-07-28 09:50:26 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 09:50:26 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-28 09:50:28 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 09:50:28 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 3086844)
2025-07-28 09:50:28 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 09:51:43 [INFO] OpenForm: 准备打开窗体 '复制及合并'，位置: Center，单实例: True
2025-07-28 09:51:43 [INFO] 开始显示窗体 '复制及合并'，位置模式: Center
2025-07-28 09:51:43 [INFO] 窗体 '复制及合并' 以TopMostForm为父窗体显示
2025-07-28 09:51:43 [INFO] 窗体 '复制及合并' 显示完成，句柄: 7407448
2025-07-28 09:51:43 [INFO] OpenForm: 窗体 '复制及合并' 打开成功
2025-07-28 09:52:26 [INFO] OpenForm: 准备打开窗体 '复制及合并'，位置: Center，单实例: True
2025-07-28 09:52:26 [INFO] 开始显示窗体 '复制及合并'，位置模式: Center
2025-07-28 09:52:26 [INFO] 窗体 '复制及合并' 以TopMostForm为父窗体显示
2025-07-28 09:52:26 [INFO] 窗体 '复制及合并' 显示完成，句柄: 2168306
2025-07-28 09:52:26 [INFO] OpenForm: 窗体 '复制及合并' 打开成功
2025-07-28 09:58:34 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 09:58:34 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 3086844, 新父窗口: 4004224
2025-07-28 09:58:34 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4004224)
2025-07-28 09:58:34 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 09:58:34 [INFO] App_WorkbookActivate: 工作簿 '★铁塔会审.xlsx' 激活处理完成
2025-07-28 09:58:34 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 09:58:34 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4004224)
2025-07-28 09:58:34 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 09:58:34 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 09:58:34 [WARN] 检测到Excel窗口句柄变化: 3086844 -> 4004224
2025-07-28 09:58:34 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4004224)
2025-07-28 09:58:34 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 09:58:35 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4004224
2025-07-28 09:58:35 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4004224)
2025-07-28 09:58:35 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 09:58:35 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 09:58:35 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 09:58:35 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4004224
2025-07-28 09:58:35 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4004224)
2025-07-28 09:58:35 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 10:03:17 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 10:03:17 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 4004224, 新父窗口: 10163446
2025-07-28 10:03:17 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10163446)
2025-07-28 10:03:17 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 10:03:17 [INFO] App_WorkbookActivate: 工作簿 '电信七期20250725.xlsx' 激活处理完成
2025-07-28 10:03:17 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 10:03:17 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10163446)
2025-07-28 10:03:17 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 10:03:17 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 10:03:17 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 10:03:17 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10163446
2025-07-28 10:03:18 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10163446)
2025-07-28 10:03:18 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 10:03:18 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 10:03:18 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 10:03:18 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10163446
2025-07-28 10:03:18 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10163446)
2025-07-28 10:03:18 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 10:20:40 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 10:20:40 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 10163446, 新父窗口: 4004224
2025-07-28 10:20:40 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4004224)
2025-07-28 10:20:40 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 10:20:40 [INFO] App_WorkbookActivate: 工作簿 '★铁塔会审.xlsx' 激活处理完成
2025-07-28 10:20:40 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 10:20:40 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4004224)
2025-07-28 10:20:40 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 10:20:40 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 10:20:40 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 10:20:40 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4004224
2025-07-28 10:20:40 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4004224)
2025-07-28 10:20:40 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 10:20:40 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 10:20:40 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 10:20:41 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4004224
2025-07-28 10:20:41 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4004224)
2025-07-28 10:20:41 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 10:29:06 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 10:29:06 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 4004224, 新父窗口: 4785806
2025-07-28 10:29:06 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 10:29:06 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 10:29:06 [INFO] App_WorkbookActivate: 工作簿 '★新站站名地址.xlsx' 激活处理完成
2025-07-28 10:29:06 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 10:29:06 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4785806)
2025-07-28 10:29:06 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 10:29:06 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 10:29:07 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 10:29:07 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 10:29:07 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 10:29:07 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 10:29:07 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 10:29:07 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 10:29:07 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4785806)
2025-07-28 10:29:07 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 10:46:58 [INFO] 会话切换事件: RemoteDisconnect
2025-07-28 10:46:58 [INFO] 会话切换事件: SessionLock
2025-07-28 12:47:13 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 12:47:14 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 12:47:14 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 12:47:14 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 12:47:14 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 12:47:14 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 12:47:14 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 12:47:14 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 12:47:14 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 12:47:14 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 12:47:14 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 12:47:14 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 12:47:14 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 12:47:14 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 12:47:14 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 12:47:14 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 12:47:14 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 12:47:14 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 12:47:14 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 12:47:14 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 12:47:14 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 12:47:14 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 12:47:14 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 12:47:14 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 12:47:14 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 12:47:14 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 12:47:14 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 12:47:14 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 12:47:14 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 12:47:14 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 12:47:15 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 12:47:15 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 12:47:15 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 12:47:15 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 12:47:15 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 12:47:15 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 12:47:15 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 12:47:15 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 12:47:15 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 12:47:15 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 12:47:15 [INFO] 会话切换事件: ConsoleConnect
2025-07-28 12:47:15 [INFO] 会话切换事件: SessionUnlock
2025-07-28 12:47:15 [INFO] 显示设置已变更
2025-07-28 12:47:16 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 12:47:16 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 12:47:16 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 12:47:16 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 12:47:16 [INFO] 显示设置变更后TopForm关系已重建
2025-07-28 12:47:16 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 12:47:16 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 12:47:16 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 12:47:17 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 12:47:17 [INFO] 会话恢复后TopForm关系已重建
2025-07-28 14:09:07 [INFO] 显示设置已变更
2025-07-28 14:09:08 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 14:09:09 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 14:09:09 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 14:09:09 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 14:09:09 [INFO] 显示设置变更后TopForm关系已重建
2025-07-28 14:11:28 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 14:11:28 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 4785806, 新父窗口: 4004224
2025-07-28 14:11:28 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4004224)
2025-07-28 14:11:28 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 14:11:28 [INFO] App_WorkbookActivate: 工作簿 '★铁塔会审.xlsx' 激活处理完成
2025-07-28 14:11:28 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 14:11:28 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4004224)
2025-07-28 14:11:28 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 14:11:28 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 14:11:28 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 14:11:29 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 14:11:29 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4004224
2025-07-28 14:11:29 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4004224)
2025-07-28 14:11:29 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 14:11:29 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 14:11:29 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4004224)
2025-07-28 14:11:29 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 14:11:29 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-28 14:11:29 [INFO] 系统事件监控已停止
2025-07-28 14:11:29 [INFO] Excel窗口句柄监控已停止
2025-07-28 14:11:29 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-28 14:11:31 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 14:11:31 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 14:11:31 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 14:11:31 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 4785806
2025-07-28 14:11:31 [INFO] 系统事件监控已启动
2025-07-28 14:11:31 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 14:11:31 [INFO] App_WorkbookActivate: 工作簿 '★新站站名地址.xlsx' 激活处理完成
2025-07-28 14:11:31 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 14:11:31 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4785806)
2025-07-28 14:11:31 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 14:11:31 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 14:11:32 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 14:11:32 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 14:11:32 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4785806
2025-07-28 14:11:32 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4785806)
2025-07-28 14:11:32 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 14:11:32 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 14:11:32 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4785806)
2025-07-28 14:11:32 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 14:11:38 [INFO] Excel窗口句柄监控器初始化完成
2025-07-28 14:11:39 [INFO] 配置文件实例已在加载时初始化
2025-07-28 14:11:39 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-07-28 14:11:39 [INFO] 🔍 === 直接测试znAbout控件状态 ===
2025-07-28 14:11:39 [INFO] 🔍 znAbout控件实例: 存在
2025-07-28 14:11:39 [INFO] 🔍 znAbout.Label: 'ZnAbout'
2025-07-28 14:11:39 [INFO] 🔍 znAbout.Name: 'znAbout'
2025-07-28 14:11:39 [INFO] 🔍 znAbout类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-28 14:11:39 [INFO] 🔍 znAboutGroup控件实例: 存在
2025-07-28 14:11:39 [INFO] 🔍 znAboutGroup.Label: '授权'
2025-07-28 14:11:39 [INFO] 🔍 znAboutGroup.Name: 'znAboutGroup'
2025-07-28 14:11:39 [INFO] 🔍 znAboutGroup类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-28 14:11:39 [INFO] 🔍 znAboutButton控件实例: 存在
2025-07-28 14:11:39 [INFO] 🔍 znAboutButton.Label: '授权'
2025-07-28 14:11:39 [INFO] 🔍 znAboutButton.Name: 'znAboutButton'
2025-07-28 14:11:39 [INFO] 🔍 znAboutButton类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-28 14:11:39 [INFO] 🔍 === znAbout控件状态测试完成 ===
2025-07-28 14:11:39 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-07-28 14:11:39 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-07-28 14:11:39 [INFO] 成功初始化Excel应用程序实例
2025-07-28 14:11:39 [INFO] 自动备份路径未配置
2025-07-28 14:11:39 [DEBUG] 开始初始化授权控制器
2025-07-28 14:11:39 [DEBUG] 授权系统初始化完成，耗时: 423ms
2025-07-28 14:11:39 [DEBUG] 开始初始化授权验证
2025-07-28 14:11:39 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-07-28 14:11:39 [DEBUG] 权限管理器初始化成功
2025-07-28 14:11:39 [DEBUG] 使用新的权限管理器进行初始化
2025-07-28 14:11:39 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-28 14:11:39 [INFO] 开始初始化UI权限管理
2025-07-28 14:11:39 [DEBUG] [实例ID: 0fcf365f] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-28 14:11:39 [DEBUG] 🔍 [实例ID: 0fcf365f] 字典引用一致性检查:
2025-07-28 14:11:39 [DEBUG] 🔍   标题映射一致性: True
2025-07-28 14:11:39 [DEBUG] 🔍   权限映射一致性: True
2025-07-28 14:11:39 [DEBUG] 🔍   信息映射一致性: True
2025-07-28 14:11:39 [DEBUG] 🔍   特殊控件一致性: True
2025-07-28 14:11:39 [DEBUG] 控件权限管理器初始化完成 [实例ID: 0fcf365f]
2025-07-28 14:11:39 [DEBUG] 开始注册控件权限映射
2025-07-28 14:11:39 [INFO] 开始初始化全局控件映射
2025-07-28 14:11:39 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-07-28 14:11:39 [DEBUG] 开始生成控件标题映射
2025-07-28 14:11:39 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-28 14:11:39 [DEBUG] 通过反射获取到 112 个字段
2025-07-28 14:11:39 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:39 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:39 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-28 14:11:39 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-28 14:11:39 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 14:11:39 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-28 14:11:39 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-28 14:11:39 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-28 14:11:39 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 14:11:39 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-28 14:11:39 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 14:11:39 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-28 14:11:39 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-28 14:11:39 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-28 14:11:39 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:39 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-28 14:11:39 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-28 14:11:39 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-28 14:11:39 [INFO] 系统事件监控已停止
2025-07-28 14:11:39 [INFO] Excel窗口句柄监控已停止
2025-07-28 14:11:39 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-28 14:11:39 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-07-28 14:11:39 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-07-28 14:11:39 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-07-28 14:11:39 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-07-28 14:11:39 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-07-28 14:11:39 [INFO] 控件标题映射生成完成，共生成 100 项映射
2025-07-28 14:11:39 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-28 14:11:39 [DEBUG] 全局控件标题映射生成完成，共生成 100 项
2025-07-28 14:11:39 [INFO] 关键控件标题映射: hyTab -> Develop
2025-07-28 14:11:39 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-07-28 14:11:39 [WARN] 关键控件未找到标题映射: buttonAbout
2025-07-28 14:11:39 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-07-28 14:11:39 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-07-28 14:11:39 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-07-28 14:11:39 [INFO] === znAbout控件标题映射诊断 ===
2025-07-28 14:11:39 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-07-28 14:11:39 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-07-28 14:11:39 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-07-28 14:11:39 [DEBUG] === 所有生成的控件标题映射 ===
2025-07-28 14:11:39 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-07-28 14:11:39 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-07-28 14:11:39 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-07-28 14:11:39 [DEBUG] 控件映射: btn发送及存档 -> '发送及存档'
2025-07-28 14:11:39 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-07-28 14:11:39 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-07-28 14:11:39 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-07-28 14:11:39 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-07-28 14:11:39 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-07-28 14:11:39 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规检查'
2025-07-28 14:11:39 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-07-28 14:11:39 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-07-28 14:11:39 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-07-28 14:11:39 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-07-28 14:11:39 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-07-28 14:11:39 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-07-28 14:11:39 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-07-28 14:11:39 [DEBUG] 控件映射: button14 -> '发送及存档'
2025-07-28 14:11:39 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-07-28 14:11:39 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-07-28 14:11:39 [DEBUG] 控件映射: button17 -> '向下填充'
2025-07-28 14:11:39 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-07-28 14:11:39 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-07-28 14:11:39 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-07-28 14:11:39 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-07-28 14:11:39 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-07-28 14:11:39 [DEBUG] 控件映射: button3 -> '关于'
2025-07-28 14:11:39 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-07-28 14:11:39 [DEBUG] 控件映射: button5 -> '文件管理'
2025-07-28 14:11:39 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-07-28 14:11:39 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-07-28 14:11:39 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-07-28 14:11:39 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-07-28 14:11:39 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-07-28 14:11:39 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-07-28 14:11:39 [DEBUG] 控件映射: button9 -> '文件快开'
2025-07-28 14:11:39 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-07-28 14:11:39 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-07-28 14:11:39 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-07-28 14:11:39 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-07-28 14:11:39 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-07-28 14:11:39 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-07-28 14:11:39 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-07-28 14:11:39 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-07-28 14:11:39 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-07-28 14:11:39 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-07-28 14:11:39 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-07-28 14:11:39 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-07-28 14:11:39 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-07-28 14:11:39 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-07-28 14:11:39 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-07-28 14:11:39 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-07-28 14:11:39 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-07-28 14:11:39 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-07-28 14:11:39 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-07-28 14:11:39 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-07-28 14:11:39 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-07-28 14:11:39 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-07-28 14:11:39 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-07-28 14:11:39 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-07-28 14:11:39 [DEBUG] 控件映射: button文件操作 -> '文件操作'
2025-07-28 14:11:39 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-07-28 14:11:39 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-07-28 14:11:39 [DEBUG] 控件映射: button专用工具 -> '专用工具'
2025-07-28 14:11:39 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-07-28 14:11:39 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-07-28 14:11:39 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-07-28 14:11:39 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-07-28 14:11:39 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-07-28 14:11:39 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-07-28 14:11:39 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-07-28 14:11:39 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-07-28 14:11:39 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-07-28 14:11:39 [DEBUG] 控件映射: group1 -> '关于'
2025-07-28 14:11:39 [DEBUG] 控件映射: group2 -> '脚本'
2025-07-28 14:11:39 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-07-28 14:11:39 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-07-28 14:11:39 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-07-28 14:11:39 [DEBUG] 控件映射: group文件 -> '文件'
2025-07-28 14:11:39 [DEBUG] 控件映射: group无线 -> '无线'
2025-07-28 14:11:39 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-07-28 14:11:39 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-07-28 14:11:39 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-07-28 14:11:39 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-07-28 14:11:39 [DEBUG] 控件映射: menu1 -> '其它'
2025-07-28 14:11:39 [DEBUG] 控件映射: menu3 -> '设置'
2025-07-28 14:11:39 [DEBUG] 控件映射: menu5 -> '修复'
2025-07-28 14:11:39 [DEBUG] 控件映射: menuHY -> '其它'
2025-07-28 14:11:39 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-07-28 14:11:39 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-07-28 14:11:39 [DEBUG] 控件映射: menu修复 -> '修复'
2025-07-28 14:11:39 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-07-28 14:11:39 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-07-28 14:11:39 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-07-28 14:11:39 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-07-28 14:11:39 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-07-28 14:11:39 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-07-28 14:11:39 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-07-28 14:11:39 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-07-28 14:11:39 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-07-28 14:11:40 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-07-28 14:11:40 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-07-28 14:11:40 [DEBUG] 开始生成控件权限映射
2025-07-28 14:11:40 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-28 14:11:40 [DEBUG] 通过反射获取到 112 个字段
2025-07-28 14:11:40 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 14:11:40 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 14:11:40 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-28 14:11:40 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-28 14:11:40 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 14:11:40 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-28 14:11:40 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-28 14:11:40 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-28 14:11:40 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 14:11:40 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-28 14:11:40 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 14:11:40 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-28 14:11:40 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-28 14:11:40 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-28 14:11:40 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 14:11:40 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-28 14:11:40 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-28 14:11:40 [INFO] 控件权限映射生成完成，共生成 104 项映射
2025-07-28 14:11:40 [DEBUG] 全局控件权限映射生成完成，共生成 104 项
2025-07-28 14:11:40 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-07-28 14:11:40 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-07-28 14:11:40 [INFO] 全局控件映射初始化完成 - 标题映射: 100 项, 权限映射: 104 项
2025-07-28 14:11:40 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-28 14:11:40 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-28 14:11:40 [INFO] 开始初始化权限验证
2025-07-28 14:11:40 [DEBUG] 设置默认UI可见性为false
2025-07-28 14:11:40 [DEBUG] 开始检查所有需要的权限
2025-07-28 14:11:40 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-28 14:11:40 [INFO] 启动网络授权信息获取任务
2025-07-28 14:11:40 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-07-28 14:11:40 [INFO] 所有权限检查完成
2025-07-28 14:11:40 [DEBUG] 应用权限状态到UI控件
2025-07-28 14:11:40 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 14:11:40 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 14:11:40 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 14:11:40 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 14:11:40 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 14:11:40 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 14:11:40 [DEBUG] 已应用权限状态到UI控件
2025-07-28 14:11:40 [DEBUG] 启动后台权限刷新任务
2025-07-28 14:11:40 [DEBUG] 启动延迟权限刷新任务
2025-07-28 14:11:40 [INFO] 权限验证初始化完成
2025-07-28 14:11:40 [INFO] UI权限管理初始化完成
2025-07-28 14:11:40 [INFO] 收到权限管理器初始化完成通知
2025-07-28 14:11:40 [INFO] 开始刷新控件标题
2025-07-28 14:11:40 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 14:11:40 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 14:11:40 [DEBUG] 控件标题刷新完成
2025-07-28 14:11:40 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 14:11:40 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 14:11:40 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 14:11:40 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 14:11:40 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 14:11:40 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 14:11:40 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 14:11:40 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 14:11:40 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 14:11:40 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 14:11:40 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 14:11:40 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 14:11:40 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 14:11:40 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 14:11:40 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 14:11:40 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 14:11:40 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 14:11:40 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 14:11:40 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 14:11:40 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 14:11:40 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 14:11:40 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 14:11:40 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 14:11:40 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 14:11:40 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 14:11:40 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 14:11:40 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 14:11:40 [INFO] 控件标题更正完成
2025-07-28 14:11:40 [INFO] 控件标题刷新完成
2025-07-28 14:11:40 [INFO] 权限管理器初始化完成处理结束
2025-07-28 14:11:40 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-28 14:11:40 [DEBUG] 授权验证初始化完成
2025-07-28 14:11:40 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-07-28 14:11:40 [INFO] 成功加载配置和授权信息
2025-07-28 14:11:40 [INFO] 开始初始化定时器和设置
2025-07-28 14:11:40 [INFO] 定时器和设置初始化完成
2025-07-28 14:11:40 [INFO] 开始VSTO插件启动流程
2025-07-28 14:11:40 [INFO] TopMostForm窗体加载完成
2025-07-28 14:11:40 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 14:11:40 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 2950524
2025-07-28 14:11:40 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 2950524)
2025-07-28 14:11:40 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 2950524
2025-07-28 14:11:40 [INFO] 系统事件监控已启动
2025-07-28 14:11:40 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 14:11:41 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-07-28 14:11:41 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-07-28 14:11:41 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-07-28 14:11:41 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 7018216
2025-07-28 14:11:41 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-07-28 14:11:41 [INFO] VSTO插件启动流程完成
2025-07-28 14:11:41 [INFO] 从Remote成功获取到网络授权信息
2025-07-28 14:11:41 [INFO] 网络授权信息已更新并触发回调
2025-07-28 14:11:41 [INFO] 网络授权信息已从 Network 更新
2025-07-28 14:11:41 [INFO] 授权版本: 1.0
2025-07-28 14:11:41 [INFO] 颁发者: ExtensionsTools
2025-07-28 14:11:41 [INFO] 用户数量: 2
2025-07-28 14:11:41 [INFO] 分组权限数量: 2
2025-07-28 14:11:41 [WARN] 配置文件中未找到用户组信息
2025-07-28 14:11:41 [INFO] 已重新设置用户组: []
2025-07-28 14:11:41 [INFO] 用户组信息已重新设置
2025-07-28 14:11:41 [INFO] 立即刷新权限缓存和UI界面
2025-07-28 14:11:41 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-28 14:11:41 [DEBUG] 使用新的权限管理器进行强制刷新
2025-07-28 14:11:41 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-07-28 14:11:41 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-28 14:11:41 [DEBUG] 本地权限缓存已清空
2025-07-28 14:11:41 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-07-28 14:11:41 [INFO] 所有权限检查完成
2025-07-28 14:11:41 [DEBUG] 权限重新检查完成
2025-07-28 14:11:41 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 14:11:41 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 14:11:41 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 14:11:41 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 14:11:41 [DEBUG] 已应用权限状态到UI控件
2025-07-28 14:11:41 [INFO] UI界面权限状态已更新
2025-07-28 14:11:41 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 14:11:41 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 14:11:41 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-07-28 14:11:41 [INFO] 权限缓存和UI界面立即刷新完成
2025-07-28 14:11:41 [INFO] 网络授权已更新，开始刷新控件标题
2025-07-28 14:11:41 [INFO] 开始刷新Ribbon控件标题
2025-07-28 14:11:41 [DEBUG] 权限缓存已清空，清除了 104 个缓存项
2025-07-28 14:11:41 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-07-28 14:11:41 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 14:11:41 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 14:11:41 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 14:11:41 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 14:11:41 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 14:11:41 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 14:11:41 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 14:11:41 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 14:11:41 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 14:11:41 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 14:11:41 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 14:11:41 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 14:11:41 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 14:11:41 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 14:11:41 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 14:11:41 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 14:11:41 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 14:11:41 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 14:11:41 [INFO] 控件标题更正完成
2025-07-28 14:11:41 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-07-28 14:11:41 [INFO] Ribbon控件标题刷新完成
2025-07-28 14:11:41 [INFO] 控件标题刷新完成
2025-07-28 14:11:41 [DEBUG] Ribbon控件标题已刷新
2025-07-28 14:11:41 [INFO] 开始刷新控件标题
2025-07-28 14:11:41 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 14:11:41 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 14:11:41 [DEBUG] 控件标题刷新完成
2025-07-28 14:11:41 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 14:11:41 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 14:11:41 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 14:11:41 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 14:11:41 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 14:11:41 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 14:11:41 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 14:11:41 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 14:11:41 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 14:11:41 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 14:11:41 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 14:11:41 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 14:11:41 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 14:11:41 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 14:11:41 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 14:11:41 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 14:11:41 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 14:11:41 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 14:11:41 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 14:11:41 [INFO] 控件标题更正完成
2025-07-28 14:11:41 [INFO] 控件标题刷新完成
2025-07-28 14:11:41 [DEBUG] Ribbon控件标题已立即刷新
2025-07-28 14:11:42 [INFO] 开始刷新授权状态
2025-07-28 14:11:42 [DEBUG] 开始初始化授权验证
2025-07-28 14:11:42 [DEBUG] 使用新的权限管理器进行初始化
2025-07-28 14:11:42 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-28 14:11:42 [INFO] 开始初始化UI权限管理
2025-07-28 14:11:42 [DEBUG] [实例ID: 9fd87fff] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-28 14:11:42 [DEBUG] 🔍 [实例ID: 9fd87fff] 字典引用一致性检查:
2025-07-28 14:11:42 [DEBUG] 🔍   标题映射一致性: True
2025-07-28 14:11:42 [DEBUG] 🔍   权限映射一致性: True
2025-07-28 14:11:42 [DEBUG] 🔍   信息映射一致性: True
2025-07-28 14:11:42 [DEBUG] 🔍   特殊控件一致性: True
2025-07-28 14:11:42 [DEBUG] 控件权限管理器初始化完成 [实例ID: 9fd87fff]
2025-07-28 14:11:42 [DEBUG] 开始注册控件权限映射
2025-07-28 14:11:42 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-28 14:11:42 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-28 14:11:42 [INFO] 开始初始化权限验证
2025-07-28 14:11:42 [DEBUG] 设置默认UI可见性为false
2025-07-28 14:11:42 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 14:11:42 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10163446
2025-07-28 14:11:42 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10163446)
2025-07-28 14:11:42 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 10163446
2025-07-28 14:11:42 [INFO] 系统事件监控已启动
2025-07-28 14:11:42 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 14:11:42 [INFO] App_WorkbookActivate: 工作簿 '电信七期20250725.xlsx' 激活处理完成
2025-07-28 14:11:42 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 14:11:42 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10163446)
2025-07-28 14:11:42 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 14:11:42 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 14:11:42 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 14:11:42 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 14:11:42 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10163446
2025-07-28 14:11:42 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10163446)
2025-07-28 14:11:42 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 14:11:42 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 14:11:42 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 2950524
2025-07-28 14:11:42 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 2950524)
2025-07-28 14:11:42 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 14:11:42 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 14:11:42 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 14:11:42 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 14:11:42 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 14:11:42 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 14:11:42 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 14:11:42 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 14:11:42 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 14:11:42 [DEBUG] 已应用权限状态到UI控件
2025-07-28 14:11:42 [DEBUG] 开始重置 208 个命令栏
2025-07-28 14:11:42 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 14:11:42 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 14:11:42 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 14:11:42 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 14:11:42 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 14:11:42 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 14:11:42 [DEBUG] 已应用权限状态到UI控件
2025-07-28 14:11:42 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10163446
2025-07-28 14:11:42 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10163446)
2025-07-28 14:11:42 [DEBUG] 开始检查所有需要的权限
2025-07-28 14:11:42 [INFO] 所有权限检查完成
2025-07-28 14:11:42 [DEBUG] 应用权限状态到UI控件
2025-07-28 14:11:42 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 14:11:42 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 14:11:42 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 14:11:42 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 14:11:42 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 14:11:42 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 14:11:42 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 14:11:42 [DEBUG] 已应用权限状态到UI控件
2025-07-28 14:11:42 [DEBUG] 启动后台权限刷新任务
2025-07-28 14:11:42 [DEBUG] 启动延迟权限刷新任务
2025-07-28 14:11:42 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 14:11:42 [INFO] 权限验证初始化完成
2025-07-28 14:11:42 [INFO] UI权限管理初始化完成
2025-07-28 14:11:42 [INFO] 收到权限管理器初始化完成通知
2025-07-28 14:11:42 [INFO] 开始刷新控件标题
2025-07-28 14:11:42 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 14:11:42 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 14:11:42 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 14:11:42 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 14:11:42 [DEBUG] 控件标题刷新完成
2025-07-28 14:11:42 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 14:11:43 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 14:11:43 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 14:11:43 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 14:11:43 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 14:11:43 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 14:11:43 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 14:11:43 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 14:11:43 [DEBUG] 已应用权限状态到UI控件
2025-07-28 14:11:43 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 14:11:43 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 14:11:43 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 14:11:43 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 14:11:43 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 14:11:43 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 14:11:43 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 14:11:43 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 14:11:43 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 14:11:43 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 14:11:43 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 14:11:43 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 14:11:43 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 14:11:43 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 14:11:43 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 14:11:43 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 14:11:43 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 14:11:43 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 14:11:43 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 14:11:43 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 14:11:43 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 14:11:43 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 14:11:43 [INFO] 控件标题更正完成
2025-07-28 14:11:43 [INFO] 控件标题刷新完成
2025-07-28 14:11:43 [INFO] 权限管理器初始化完成处理结束
2025-07-28 14:11:43 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-28 14:11:43 [DEBUG] 授权验证初始化完成
2025-07-28 14:11:43 [INFO] 授权状态刷新完成
2025-07-28 14:11:43 [DEBUG] 重置命令栏: cell
2025-07-28 14:11:43 [DEBUG] 重置命令栏: column
2025-07-28 14:11:43 [DEBUG] 重置命令栏: row
2025-07-28 14:11:43 [DEBUG] 重置命令栏: cell
2025-07-28 14:11:43 [DEBUG] 重置命令栏: column
2025-07-28 14:11:43 [DEBUG] 重置命令栏: row
2025-07-28 14:11:43 [DEBUG] 重置命令栏: row
2025-07-28 14:11:43 [DEBUG] 重置命令栏: column
2025-07-28 14:11:43 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-07-28 14:11:44 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 14:11:44 [DEBUG] 授权控制器已初始化
2025-07-28 14:11:44 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 14:11:44 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 14:11:44 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 14:11:44 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 14:11:44 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 14:11:44 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 14:11:44 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 14:11:44 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 14:11:44 [DEBUG] 已应用权限状态到UI控件
2025-07-28 14:11:45 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 14:11:45 [DEBUG] 授权控制器已初始化
2025-07-28 14:11:45 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 14:11:45 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 14:11:45 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 14:11:45 [DEBUG] 授权控制器已初始化
2025-07-28 14:11:45 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 14:11:46 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 14:11:46 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 14:11:46 [DEBUG] 授权控制器已初始化
2025-07-28 14:11:46 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 14:11:47 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 14:11:47 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-28 14:11:47 [INFO] 系统事件监控已停止
2025-07-28 14:11:47 [INFO] Excel窗口句柄监控已停止
2025-07-28 14:11:47 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-28 14:11:47 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 14:11:47 [DEBUG] 授权控制器已初始化
2025-07-28 14:11:47 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 14:11:47 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 14:11:48 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 14:11:48 [DEBUG] 授权控制器已初始化
2025-07-28 14:11:48 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 14:11:48 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 14:11:48 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 14:11:48 [DEBUG] 授权控制器已初始化
2025-07-28 14:11:48 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 14:11:49 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 14:11:49 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3086844
2025-07-28 14:11:49 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3086844)
2025-07-28 14:11:49 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 3086844
2025-07-28 14:11:49 [INFO] 系统事件监控已启动
2025-07-28 14:11:49 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 14:11:49 [INFO] App_WorkbookActivate: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 激活处理完成
2025-07-28 14:11:49 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 14:11:49 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 3086844)
2025-07-28 14:11:49 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 14:11:49 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 14:11:49 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 14:11:49 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 14:11:49 [DEBUG] 授权控制器已初始化
2025-07-28 14:11:49 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 14:11:49 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 14:11:49 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3086844
2025-07-28 14:11:49 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3086844)
2025-07-28 14:11:49 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 14:11:49 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 14:11:49 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 14:11:49 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 14:11:49 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3086844
2025-07-28 14:11:49 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3086844)
2025-07-28 14:11:49 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 14:11:50 [DEBUG] 已重置工作表标签菜单
2025-07-28 14:11:50 [DEBUG] 工作表标签菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 14:11:51 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 14:11:51 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3086844
2025-07-28 14:11:51 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3086844)
2025-07-28 14:11:51 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 14:11:51 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 14:11:52 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 14:11:52 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3086844
2025-07-28 14:11:52 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3086844)
2025-07-28 14:11:52 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 14:11:52 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 14:11:57 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-28 14:11:57 [INFO] 系统事件监控已停止
2025-07-28 14:11:57 [INFO] Excel窗口句柄监控已停止
2025-07-28 14:11:57 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-28 14:12:00 [INFO] 开始VSTO插件关闭流程
2025-07-28 14:12:00 [INFO] 程序集追踪日志已保存到: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\logs\AssemblyTrace_20250728_141200.txt
2025-07-28 14:12:00 [INFO] VSTO插件关闭流程完成
2025-07-28 14:12:34 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 14:12:34 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 14:12:34 [INFO] OpenForm: 准备打开窗体 '合规检查'，位置: Right，单实例: True
2025-07-28 14:12:34 [INFO] 开始显示窗体 '合规检查'，位置模式: Right
2025-07-28 14:12:34 [INFO] 窗体 '合规检查' 以TopMostForm为父窗体显示
2025-07-28 14:12:34 [INFO] 窗体 '合规检查' 显示完成，句柄: 10556852
2025-07-28 14:12:34 [INFO] OpenForm: 窗体 '合规检查' 打开成功
2025-07-28 14:13:43 [ERROR] 处理UI更新时发生错误
异常详情：System.Runtime.InteropServices.COMException (0x800A03EC): 异常来自 HRESULT:0x800A03EC
   在 System.RuntimeType.ForwardCallToInvokeMember(String memberName, BindingFlags flags, Object target, Int32[] aWrapperTypes, MessageData& msgData)
   在 Microsoft.Office.Interop.Excel.Pane.PointsToScreenPixelsX(Int32 Points)
   在 ET.ETForm.RangeRect(Application xlApp, Range inputRange) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 288
   在 HyExcelVsto.ThisAddIn.HandleUIUpdates(Range selectionRange, Range activeCell, Range target) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ThisAddIn.cs:行号 1142
2025-07-28 14:14:00 [INFO] OpenForm: 准备打开窗体 '批量查找'，位置: Right，单实例: True
2025-07-28 14:14:00 [INFO] 开始显示窗体 '批量查找'，位置模式: Right
2025-07-28 14:14:01 [INFO] 窗体 '批量查找' 以TopMostForm为父窗体显示
2025-07-28 14:14:01 [INFO] 窗体 '批量查找' 显示完成，句柄: 787668
2025-07-28 14:14:01 [INFO] OpenForm: 窗体 '批量查找' 打开成功
2025-07-28 14:14:43 [INFO] OpenForm: 准备打开窗体 '设置页眉页脚'，位置: Center，单实例: True
2025-07-28 14:14:43 [INFO] 开始显示窗体 '设置页眉页脚'，位置模式: Center
2025-07-28 14:14:43 [INFO] 窗体 '设置页眉页脚' 以TopMostForm为父窗体显示
2025-07-28 14:14:43 [INFO] 窗体 '设置页眉页脚' 显示完成，句柄: 19143198
2025-07-28 14:14:43 [INFO] OpenForm: 窗体 '设置页眉页脚' 打开成功
2025-07-28 15:17:51 [INFO] Excel窗口句柄监控器初始化完成
2025-07-28 15:17:52 [INFO] 配置文件实例已在加载时初始化
2025-07-28 15:17:52 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-07-28 15:17:52 [INFO] 🔍 === 直接测试znAbout控件状态 ===
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件实例: 存在
2025-07-28 15:17:52 [INFO] 🔍 znAbout.Label: 'ZnAbout'
2025-07-28 15:17:52 [INFO] 🔍 znAbout.Name: 'znAbout'
2025-07-28 15:17:52 [INFO] 🔍 znAbout类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-28 15:17:52 [INFO] 🔍 znAboutGroup控件实例: 存在
2025-07-28 15:17:52 [INFO] 🔍 znAboutGroup.Label: '授权'
2025-07-28 15:17:52 [INFO] 🔍 znAboutGroup.Name: 'znAboutGroup'
2025-07-28 15:17:52 [INFO] 🔍 znAboutGroup类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-28 15:17:52 [INFO] 🔍 znAboutButton控件实例: 存在
2025-07-28 15:17:52 [INFO] 🔍 znAboutButton.Label: '授权'
2025-07-28 15:17:52 [INFO] 🔍 znAboutButton.Name: 'znAboutButton'
2025-07-28 15:17:52 [INFO] 🔍 znAboutButton类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-28 15:17:52 [INFO] 🔍 === znAbout控件状态测试完成 ===
2025-07-28 15:17:52 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-07-28 15:17:52 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-07-28 15:17:52 [INFO] 成功初始化Excel应用程序实例
2025-07-28 15:17:52 [INFO] 自动备份路径未配置
2025-07-28 15:17:52 [DEBUG] 开始初始化授权控制器
2025-07-28 15:17:52 [DEBUG] 授权系统初始化完成，耗时: 302ms
2025-07-28 15:17:52 [DEBUG] 开始初始化授权验证
2025-07-28 15:17:52 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-07-28 15:17:52 [DEBUG] 权限管理器初始化成功
2025-07-28 15:17:52 [DEBUG] 使用新的权限管理器进行初始化
2025-07-28 15:17:52 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-28 15:17:52 [INFO] 开始初始化UI权限管理
2025-07-28 15:17:52 [DEBUG] [实例ID: dcc60f46] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-28 15:17:52 [DEBUG] 🔍 [实例ID: dcc60f46] 字典引用一致性检查:
2025-07-28 15:17:52 [DEBUG] 🔍   标题映射一致性: True
2025-07-28 15:17:52 [DEBUG] 🔍   权限映射一致性: True
2025-07-28 15:17:52 [DEBUG] 🔍   信息映射一致性: True
2025-07-28 15:17:52 [DEBUG] 🔍   特殊控件一致性: True
2025-07-28 15:17:52 [DEBUG] 控件权限管理器初始化完成 [实例ID: dcc60f46]
2025-07-28 15:17:52 [DEBUG] 开始注册控件权限映射
2025-07-28 15:17:52 [INFO] 开始初始化全局控件映射
2025-07-28 15:17:52 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-07-28 15:17:52 [DEBUG] 开始生成控件标题映射
2025-07-28 15:17:52 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-28 15:17:52 [DEBUG] 通过反射获取到 112 个字段
2025-07-28 15:17:52 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-28 15:17:52 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-28 15:17:52 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-28 15:17:52 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-28 15:17:52 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-28 15:17:52 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-28 15:17:52 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-28 15:17:52 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-07-28 15:17:52 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-07-28 15:17:52 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-07-28 15:17:52 [INFO] 控件标题映射生成完成，共生成 100 项映射
2025-07-28 15:17:52 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-28 15:17:52 [DEBUG] 全局控件标题映射生成完成，共生成 100 项
2025-07-28 15:17:52 [INFO] 关键控件标题映射: hyTab -> Develop
2025-07-28 15:17:52 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-07-28 15:17:52 [WARN] 关键控件未找到标题映射: buttonAbout
2025-07-28 15:17:52 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-07-28 15:17:52 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-07-28 15:17:52 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-07-28 15:17:52 [INFO] === znAbout控件标题映射诊断 ===
2025-07-28 15:17:52 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-07-28 15:17:52 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-07-28 15:17:52 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-07-28 15:17:52 [DEBUG] === 所有生成的控件标题映射 ===
2025-07-28 15:17:52 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-07-28 15:17:52 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-07-28 15:17:52 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-07-28 15:17:52 [DEBUG] 控件映射: btn发送及存档 -> '发送及存档'
2025-07-28 15:17:52 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-07-28 15:17:52 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-07-28 15:17:52 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-07-28 15:17:52 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-07-28 15:17:52 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-07-28 15:17:52 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规检查'
2025-07-28 15:17:52 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-07-28 15:17:52 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-07-28 15:17:52 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-07-28 15:17:52 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-07-28 15:17:52 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-07-28 15:17:52 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-07-28 15:17:52 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-07-28 15:17:52 [DEBUG] 控件映射: button14 -> '发送及存档'
2025-07-28 15:17:52 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-07-28 15:17:52 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-07-28 15:17:52 [DEBUG] 控件映射: button17 -> '向下填充'
2025-07-28 15:17:52 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-07-28 15:17:52 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-07-28 15:17:52 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-07-28 15:17:52 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-07-28 15:17:52 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-07-28 15:17:52 [DEBUG] 控件映射: button3 -> '关于'
2025-07-28 15:17:52 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-07-28 15:17:52 [DEBUG] 控件映射: button5 -> '文件管理'
2025-07-28 15:17:52 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-07-28 15:17:52 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-07-28 15:17:52 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-07-28 15:17:52 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-07-28 15:17:52 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-07-28 15:17:52 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-07-28 15:17:52 [DEBUG] 控件映射: button9 -> '文件快开'
2025-07-28 15:17:52 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-07-28 15:17:52 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-07-28 15:17:52 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-07-28 15:17:52 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-07-28 15:17:52 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-07-28 15:17:52 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-07-28 15:17:52 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-07-28 15:17:52 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-07-28 15:17:52 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-07-28 15:17:52 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-07-28 15:17:52 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-07-28 15:17:52 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-07-28 15:17:52 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-07-28 15:17:52 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-07-28 15:17:52 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-07-28 15:17:52 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-07-28 15:17:52 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-07-28 15:17:52 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-07-28 15:17:52 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-07-28 15:17:52 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-07-28 15:17:52 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-07-28 15:17:52 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-07-28 15:17:52 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-07-28 15:17:52 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-07-28 15:17:52 [DEBUG] 控件映射: button文件操作 -> '文件操作'
2025-07-28 15:17:52 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-07-28 15:17:52 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-07-28 15:17:52 [DEBUG] 控件映射: button专用工具 -> '专用工具'
2025-07-28 15:17:52 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-07-28 15:17:52 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-07-28 15:17:52 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-07-28 15:17:52 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-07-28 15:17:52 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-07-28 15:17:52 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-07-28 15:17:52 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-07-28 15:17:52 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-07-28 15:17:52 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-07-28 15:17:52 [DEBUG] 控件映射: group1 -> '关于'
2025-07-28 15:17:52 [DEBUG] 控件映射: group2 -> '脚本'
2025-07-28 15:17:52 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-07-28 15:17:52 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-07-28 15:17:52 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-07-28 15:17:52 [DEBUG] 控件映射: group文件 -> '文件'
2025-07-28 15:17:52 [DEBUG] 控件映射: group无线 -> '无线'
2025-07-28 15:17:52 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-07-28 15:17:52 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-07-28 15:17:52 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-07-28 15:17:52 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-07-28 15:17:52 [DEBUG] 控件映射: menu1 -> '其它'
2025-07-28 15:17:52 [DEBUG] 控件映射: menu3 -> '设置'
2025-07-28 15:17:52 [DEBUG] 控件映射: menu5 -> '修复'
2025-07-28 15:17:52 [DEBUG] 控件映射: menuHY -> '其它'
2025-07-28 15:17:52 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-07-28 15:17:52 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-07-28 15:17:52 [DEBUG] 控件映射: menu修复 -> '修复'
2025-07-28 15:17:52 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-07-28 15:17:52 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-07-28 15:17:52 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-07-28 15:17:52 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-07-28 15:17:52 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-07-28 15:17:52 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-07-28 15:17:52 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-07-28 15:17:52 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-07-28 15:17:52 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-07-28 15:17:52 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-07-28 15:17:52 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-07-28 15:17:52 [DEBUG] 开始生成控件权限映射
2025-07-28 15:17:52 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-28 15:17:52 [DEBUG] 通过反射获取到 112 个字段
2025-07-28 15:17:52 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 15:17:52 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-28 15:17:52 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-28 15:17:52 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-28 15:17:52 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-28 15:17:52 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-28 15:17:52 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-28 15:17:52 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 15:17:52 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-28 15:17:52 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-28 15:17:52 [INFO] 控件权限映射生成完成，共生成 104 项映射
2025-07-28 15:17:52 [DEBUG] 全局控件权限映射生成完成，共生成 104 项
2025-07-28 15:17:52 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-07-28 15:17:52 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-07-28 15:17:52 [INFO] 全局控件映射初始化完成 - 标题映射: 100 项, 权限映射: 104 项
2025-07-28 15:17:52 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-28 15:17:52 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-28 15:17:52 [INFO] 开始初始化权限验证
2025-07-28 15:17:52 [DEBUG] 设置默认UI可见性为false
2025-07-28 15:17:52 [DEBUG] 开始检查所有需要的权限
2025-07-28 15:17:52 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-28 15:17:53 [INFO] 启动网络授权信息获取任务
2025-07-28 15:17:53 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-07-28 15:17:53 [INFO] 所有权限检查完成
2025-07-28 15:17:53 [DEBUG] 应用权限状态到UI控件
2025-07-28 15:17:53 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 15:17:53 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 15:17:53 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 15:17:53 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 15:17:53 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 15:17:53 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 15:17:53 [DEBUG] 已应用权限状态到UI控件
2025-07-28 15:17:53 [DEBUG] 启动后台权限刷新任务
2025-07-28 15:17:53 [DEBUG] 启动延迟权限刷新任务
2025-07-28 15:17:53 [INFO] 权限验证初始化完成
2025-07-28 15:17:53 [INFO] UI权限管理初始化完成
2025-07-28 15:17:53 [INFO] 收到权限管理器初始化完成通知
2025-07-28 15:17:53 [INFO] 开始刷新控件标题
2025-07-28 15:17:53 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 15:17:53 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 15:17:53 [DEBUG] 控件标题刷新完成
2025-07-28 15:17:53 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 15:17:53 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 15:17:53 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 15:17:53 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 15:17:53 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 15:17:53 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 15:17:53 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 15:17:53 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 15:17:53 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 15:17:53 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 15:17:53 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 15:17:53 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 15:17:53 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 15:17:53 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 15:17:53 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 15:17:53 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 15:17:53 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 15:17:53 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 15:17:53 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 15:17:53 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 15:17:53 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 15:17:53 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 15:17:53 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 15:17:53 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 15:17:53 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 15:17:53 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 15:17:53 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 15:17:53 [INFO] 控件标题更正完成
2025-07-28 15:17:53 [INFO] 控件标题刷新完成
2025-07-28 15:17:53 [INFO] 权限管理器初始化完成处理结束
2025-07-28 15:17:53 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-28 15:17:53 [DEBUG] 授权验证初始化完成
2025-07-28 15:17:53 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-07-28 15:17:53 [INFO] 成功加载配置和授权信息
2025-07-28 15:17:53 [INFO] 开始初始化定时器和设置
2025-07-28 15:17:53 [INFO] 定时器和设置初始化完成
2025-07-28 15:17:53 [INFO] 开始VSTO插件启动流程
2025-07-28 15:17:53 [INFO] TopMostForm窗体加载完成
2025-07-28 15:17:53 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:17:53 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:17:53 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:17:53 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 1776374
2025-07-28 15:17:53 [INFO] 系统事件监控已启动
2025-07-28 15:17:53 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:17:53 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-07-28 15:17:53 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-07-28 15:17:53 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-07-28 15:17:53 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 659974
2025-07-28 15:17:53 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-07-28 15:17:53 [INFO] VSTO插件启动流程完成
2025-07-28 15:17:53 [INFO] 从Remote成功获取到网络授权信息
2025-07-28 15:17:53 [INFO] 网络授权信息已更新并触发回调
2025-07-28 15:17:53 [INFO] App_WorkbookOpen: 工作簿 '2025年高铁问题管理表梅汕高铁(1).xlsx' 打开事件触发
2025-07-28 15:17:53 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:17:53 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1776374)
2025-07-28 15:17:53 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:17:53 [INFO] App_WorkbookOpen: 工作簿 '2025年高铁问题管理表梅汕高铁(1).xlsx' 打开处理完成
2025-07-28 15:17:53 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:17:53 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1776374)
2025-07-28 15:17:53 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:17:53 [INFO] App_WorkbookActivate: 工作簿 '2025年高铁问题管理表梅汕高铁(1).xlsx' 激活处理完成
2025-07-28 15:17:53 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:17:53 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1776374)
2025-07-28 15:17:53 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:17:53 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 15:17:53 [INFO] 网络授权信息已从 Network 更新
2025-07-28 15:17:53 [INFO] 授权版本: 1.0
2025-07-28 15:17:53 [INFO] 颁发者: ExtensionsTools
2025-07-28 15:17:53 [INFO] 用户数量: 2
2025-07-28 15:17:53 [INFO] 分组权限数量: 2
2025-07-28 15:17:53 [WARN] 配置文件中未找到用户组信息
2025-07-28 15:17:53 [INFO] 已重新设置用户组: []
2025-07-28 15:17:53 [INFO] 用户组信息已重新设置
2025-07-28 15:17:53 [INFO] 立即刷新权限缓存和UI界面
2025-07-28 15:17:53 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-28 15:17:53 [DEBUG] 使用新的权限管理器进行强制刷新
2025-07-28 15:17:53 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-07-28 15:17:53 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-28 15:17:53 [DEBUG] 本地权限缓存已清空
2025-07-28 15:17:53 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-07-28 15:17:54 [INFO] 所有权限检查完成
2025-07-28 15:17:54 [DEBUG] 权限重新检查完成
2025-07-28 15:17:54 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 15:17:54 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 15:17:54 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 15:17:54 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 15:17:54 [DEBUG] 已应用权限状态到UI控件
2025-07-28 15:17:54 [INFO] UI界面权限状态已更新
2025-07-28 15:17:54 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 15:17:54 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 15:17:54 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-07-28 15:17:54 [INFO] 权限缓存和UI界面立即刷新完成
2025-07-28 15:17:54 [INFO] 网络授权已更新，开始刷新控件标题
2025-07-28 15:17:54 [INFO] 开始刷新Ribbon控件标题
2025-07-28 15:17:54 [DEBUG] 权限缓存已清空，清除了 104 个缓存项
2025-07-28 15:17:54 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-07-28 15:17:54 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 15:17:54 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 15:17:54 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 15:17:54 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 15:17:54 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 15:17:54 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 15:17:54 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 15:17:54 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 15:17:54 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 15:17:54 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 15:17:54 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 15:17:54 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 15:17:54 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 15:17:54 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 15:17:54 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 15:17:54 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 15:17:54 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 15:17:54 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 15:17:54 [INFO] 控件标题更正完成
2025-07-28 15:17:54 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-07-28 15:17:54 [INFO] Ribbon控件标题刷新完成
2025-07-28 15:17:54 [INFO] 控件标题刷新完成
2025-07-28 15:17:54 [DEBUG] Ribbon控件标题已刷新
2025-07-28 15:17:54 [INFO] 开始刷新控件标题
2025-07-28 15:17:54 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 15:17:54 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 15:17:54 [DEBUG] 控件标题刷新完成
2025-07-28 15:17:54 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 15:17:54 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 15:17:54 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 15:17:54 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 15:17:54 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 15:17:54 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 15:17:54 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 15:17:54 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 15:17:54 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 15:17:54 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 15:17:54 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 15:17:54 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 15:17:54 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 15:17:54 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 15:17:54 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 15:17:54 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 15:17:54 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 15:17:54 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 15:17:54 [INFO] 控件标题更正完成
2025-07-28 15:17:54 [INFO] 控件标题刷新完成
2025-07-28 15:17:54 [DEBUG] Ribbon控件标题已立即刷新
2025-07-28 15:17:54 [INFO] 开始刷新授权状态
2025-07-28 15:17:54 [DEBUG] 开始初始化授权验证
2025-07-28 15:17:54 [DEBUG] 使用新的权限管理器进行初始化
2025-07-28 15:17:54 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-28 15:17:54 [INFO] 开始初始化UI权限管理
2025-07-28 15:17:54 [DEBUG] [实例ID: db0a46ed] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-28 15:17:54 [DEBUG] 🔍 [实例ID: db0a46ed] 字典引用一致性检查:
2025-07-28 15:17:54 [DEBUG] 🔍   标题映射一致性: True
2025-07-28 15:17:54 [DEBUG] 🔍   权限映射一致性: True
2025-07-28 15:17:54 [DEBUG] 🔍   信息映射一致性: True
2025-07-28 15:17:54 [DEBUG] 🔍   特殊控件一致性: True
2025-07-28 15:17:54 [DEBUG] 控件权限管理器初始化完成 [实例ID: db0a46ed]
2025-07-28 15:17:54 [DEBUG] 开始注册控件权限映射
2025-07-28 15:17:54 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-28 15:17:54 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-28 15:17:54 [INFO] 开始初始化权限验证
2025-07-28 15:17:54 [DEBUG] 设置默认UI可见性为false
2025-07-28 15:17:54 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:17:54 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:17:54 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 15:17:54 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 15:17:54 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 15:17:54 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 15:17:54 [DEBUG] 已应用权限状态到UI控件
2025-07-28 15:17:54 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:17:54 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1776374)
2025-07-28 15:17:54 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:17:54 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:17:54 [DEBUG] 开始重置 209 个命令栏
2025-07-28 15:17:54 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:17:54 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 15:17:54 [DEBUG] 开始检查所有需要的权限
2025-07-28 15:17:54 [INFO] 所有权限检查完成
2025-07-28 15:17:54 [DEBUG] 应用权限状态到UI控件
2025-07-28 15:17:54 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 15:17:54 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 15:17:54 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 15:17:54 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 15:17:54 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:17:54 [DEBUG] 已应用权限状态到UI控件
2025-07-28 15:17:54 [DEBUG] 启动后台权限刷新任务
2025-07-28 15:17:54 [DEBUG] 启动延迟权限刷新任务
2025-07-28 15:17:54 [INFO] 权限验证初始化完成
2025-07-28 15:17:54 [INFO] UI权限管理初始化完成
2025-07-28 15:17:54 [INFO] 收到权限管理器初始化完成通知
2025-07-28 15:17:54 [INFO] 开始刷新控件标题
2025-07-28 15:17:54 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 15:17:54 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 15:17:54 [DEBUG] 控件标题刷新完成
2025-07-28 15:17:54 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 15:17:54 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 15:17:54 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 15:17:54 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 15:17:54 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 15:17:54 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 15:17:54 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 15:17:54 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 15:17:54 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 15:17:54 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 15:17:54 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 15:17:54 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 15:17:54 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 15:17:54 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 15:17:54 [DEBUG] 已应用权限状态到UI控件
2025-07-28 15:17:54 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 15:17:54 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 15:17:54 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 15:17:54 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 15:17:54 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 15:17:54 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 15:17:54 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 15:17:54 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 15:17:54 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 15:17:54 [INFO] 控件标题更正完成
2025-07-28 15:17:54 [INFO] 控件标题刷新完成
2025-07-28 15:17:54 [INFO] 权限管理器初始化完成处理结束
2025-07-28 15:17:54 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-28 15:17:54 [DEBUG] 授权验证初始化完成
2025-07-28 15:17:54 [INFO] 授权状态刷新完成
2025-07-28 15:17:54 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:17:54 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:17:54 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:17:54 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-28 15:17:54 [DEBUG] 重置命令栏: cell
2025-07-28 15:17:54 [DEBUG] 重置命令栏: column
2025-07-28 15:17:54 [DEBUG] 重置命令栏: row
2025-07-28 15:17:55 [DEBUG] 重置命令栏: cell
2025-07-28 15:17:55 [DEBUG] 重置命令栏: column
2025-07-28 15:17:55 [DEBUG] 重置命令栏: row
2025-07-28 15:17:55 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 15:17:55 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 15:17:55 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 15:17:55 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 15:17:55 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 15:17:55 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 15:17:55 [DEBUG] 已应用权限状态到UI控件
2025-07-28 15:17:55 [DEBUG] 重置命令栏: row
2025-07-28 15:17:55 [DEBUG] 重置命令栏: column
2025-07-28 15:17:55 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-07-28 15:17:56 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 15:17:56 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 15:17:56 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 15:17:56 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 15:17:56 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 15:17:56 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 15:17:56 [DEBUG] 已应用权限状态到UI控件
2025-07-28 15:17:57 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 15:17:57 [DEBUG] 授权控制器已初始化
2025-07-28 15:17:57 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 15:17:58 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 15:17:58 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 15:17:58 [DEBUG] 授权控制器已初始化
2025-07-28 15:17:58 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 15:17:59 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 15:17:59 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 15:17:59 [DEBUG] 授权控制器已初始化
2025-07-28 15:17:59 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 15:17:59 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 15:18:00 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 15:18:00 [DEBUG] 授权控制器已初始化
2025-07-28 15:18:00 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 15:18:01 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 15:18:02 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 15:18:02 [DEBUG] 授权控制器已初始化
2025-07-28 15:18:02 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 15:18:02 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 15:18:03 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 15:18:03 [DEBUG] 授权控制器已初始化
2025-07-28 15:18:03 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 15:18:03 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 15:18:04 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 15:18:04 [DEBUG] 授权控制器已初始化
2025-07-28 15:18:04 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 15:18:04 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 15:18:05 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 15:18:05 [DEBUG] 授权控制器已初始化
2025-07-28 15:18:05 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 15:18:05 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 15:18:06 [DEBUG] 已重置工作表标签菜单
2025-07-28 15:18:06 [DEBUG] 工作表标签菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 15:19:10 [INFO] OpenForm: 准备打开窗体 'ExcelFileManager'，位置: Center，单实例: True
2025-07-28 15:19:10 [INFO] 开始显示窗体 'ExcelFileManager'，位置模式: Center
2025-07-28 15:19:11 [INFO] 窗体 'ExcelFileManager' 以TopMostForm为父窗体显示
2025-07-28 15:19:11 [INFO] 窗体 'ExcelFileManager' 显示完成，句柄: 7476562
2025-07-28 15:19:11 [INFO] OpenForm: 窗体 'ExcelFileManager' 打开成功
2025-07-28 15:19:18 [INFO] App_WorkbookOpen: 工作簿 '★铁塔会审.xlsx' 打开事件触发
2025-07-28 15:19:18 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:19:18 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 1776374, 新父窗口: 6816868
2025-07-28 15:19:18 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 15:19:18 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:19:18 [INFO] App_WorkbookOpen: 工作簿 '★铁塔会审.xlsx' 打开处理完成
2025-07-28 15:19:18 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:19:18 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 6816868)
2025-07-28 15:19:18 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:19:18 [INFO] App_WorkbookActivate: 工作簿 '★铁塔会审.xlsx' 激活处理完成
2025-07-28 15:19:18 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:19:18 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 6816868)
2025-07-28 15:19:18 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:19:18 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 15:19:18 [WARN] 检测到Excel窗口句柄变化: 1776374 -> 6816868
2025-07-28 15:19:18 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 6816868)
2025-07-28 15:19:18 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:19:18 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:19:18 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6816868
2025-07-28 15:19:18 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 15:19:18 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:19:18 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 15:19:18 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 6816868)
2025-07-28 15:19:18 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:19:19 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:19:19 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6816868
2025-07-28 15:19:19 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 15:19:19 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:19:19 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-07-28 15:19:30 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl控件初始化完成
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志级别设置为：Info
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETLogDisplayControl] 最大日志行数设置为：1000
2025-07-28 15:19:30 [INFO] [ET.Controls.ETLogDisplayControl] KML转换器已就绪
2025-07-28 15:19:30 [INFO] [ET.Controls.ETLogDisplayControl] 功能说明：为KML文件中的地标添加备注信息
2025-07-28 15:19:30 [INFO] [ET.Controls.ETLogDisplayControl] 备注格式：地标名称 + 经纬度 + 区域信息
2025-07-28 15:19:30 [INFO] [ET.Controls.ETLogDisplayControl] 请先选择来源KML文件，目标文件路径将自动生成
2025-07-28 15:19:30 [INFO] [HyExcelVsto.Module.WX.frmGPS生成图层, Text: GPS生成图层] KML转换器初始化完成
2025-07-28 15:19:30 [INFO] OpenForm: 准备打开窗体 'GPS生成图层'，位置: Center，单实例: True
2025-07-28 15:19:30 [INFO] 开始显示窗体 'GPS生成图层'，位置模式: Center
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：'会审记录2024'!$K:$L
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：'会审记录2024'!$K:$L，触发事件：True
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：'会审记录2024'!$J:$J
2025-07-28 15:19:30 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：'会审记录2024'!$J:$J，触发事件：True
2025-07-28 15:19:30 [INFO] 窗体 'GPS生成图层' 以TopMostForm为父窗体显示
2025-07-28 15:19:30 [INFO] 窗体 'GPS生成图层' 显示完成，句柄: 7737246
2025-07-28 15:19:30 [INFO] OpenForm: 窗体 'GPS生成图层' 打开成功
2025-07-28 15:19:37 [DEBUG] [ET.Controls.ETRangeSelectControl] 隐藏父窗体，原高度：228
2025-07-28 15:19:37 [DEBUG] [ET.Controls.ETRangeSelectControl] 开始显示Excel选择对话框
2025-07-28 15:19:37 [INFO] [ET.Controls.ETRangeSelectControl] ✅ 成功获取应用程序实例 - 名称: Microsoft Excel, 版本: 16.0, WPS环境: False
2025-07-28 15:19:37 [DEBUG] [ET.Controls.ETRangeSelectControl] 调用InputBox进行范围选择
2025-07-28 15:19:39 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：'会审记录2024'!$M:$M
2025-07-28 15:19:39 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：'会审记录2024'!$M:$M，触发事件：True
2025-07-28 15:19:39 [INFO] [ET.Controls.ETRangeSelectControl] 选择有效范围：'会审记录2024'!$M:$M
2025-07-28 15:19:39 [DEBUG] [ET.Controls.ETRangeSelectControl] 尝试激活Excel应用程序窗口
2025-07-28 15:19:39 [DEBUG] [ET.Controls.ETRangeSelectControl] 恢复父窗体显示，高度：228
2025-07-28 15:19:39 [INFO] [ET.Controls.ETRangeSelectControl] 选择对话框完成，结果：成功
2025-07-28 15:19:39 [INFO] [ET.Controls.ETRangeSelectControl] 用户点击选择按钮
2025-07-28 15:22:00 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:22:00 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 6816868, 新父窗口: 1776374
2025-07-28 15:22:00 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:22:00 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:22:00 [INFO] App_WorkbookActivate: 工作簿 '2025年高铁问题管理表梅汕高铁(1).xlsx' 激活处理完成
2025-07-28 15:22:00 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:22:00 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1776374)
2025-07-28 15:22:00 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:22:00 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 15:22:01 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:22:01 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:22:01 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:22:01 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:22:01 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 15:22:01 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:22:01 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:22:01 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:22:01 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:22:38 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl正在释放资源
2025-07-28 15:30:12 [INFO] App_WorkbookOpen: 工作簿 '2025年高铁问题管理表梅汕高铁.xlsx' 打开事件触发
2025-07-28 15:30:12 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:30:12 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 1776374, 新父窗口: 10489976
2025-07-28 15:30:12 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10489976)
2025-07-28 15:30:12 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:30:12 [INFO] App_WorkbookOpen: 工作簿 '2025年高铁问题管理表梅汕高铁.xlsx' 打开处理完成
2025-07-28 15:30:12 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:30:12 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10489976)
2025-07-28 15:30:12 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:30:12 [INFO] App_WorkbookActivate: 工作簿 '2025年高铁问题管理表梅汕高铁.xlsx' 激活处理完成
2025-07-28 15:30:12 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:30:12 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10489976)
2025-07-28 15:30:12 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:30:12 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 15:30:12 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:30:13 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10489976
2025-07-28 15:30:13 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10489976)
2025-07-28 15:30:13 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:30:13 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:30:13 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 15:30:13 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10489976
2025-07-28 15:30:13 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10489976)
2025-07-28 15:30:13 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:30:13 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:30:13 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10489976
2025-07-28 15:30:13 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10489976)
2025-07-28 15:30:13 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:30:13 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-28 15:30:16 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:30:16 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 10489976, 新父窗口: 6816868
2025-07-28 15:30:16 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 15:30:16 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:30:16 [INFO] App_WorkbookActivate: 工作簿 '★铁塔会审.xlsx' 激活处理完成
2025-07-28 15:30:16 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:30:16 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 6816868)
2025-07-28 15:30:16 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:30:16 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 15:30:16 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:30:16 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6816868
2025-07-28 15:30:16 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 15:30:16 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:30:16 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 15:30:16 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:30:16 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6816868
2025-07-28 15:30:16 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 15:30:16 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:30:16 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:30:17 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6816868
2025-07-28 15:30:17 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 15:30:17 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:30:17 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 15:30:18 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:30:18 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 6816868, 新父窗口: 10489976
2025-07-28 15:30:18 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10489976)
2025-07-28 15:30:18 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:30:18 [INFO] App_WorkbookActivate: 工作簿 '2025年高铁问题管理表梅汕高铁.xlsx' 激活处理完成
2025-07-28 15:30:18 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:30:18 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10489976)
2025-07-28 15:30:18 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:30:18 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 15:30:18 [WARN] 检测到Excel窗口句柄变化: 6816868 -> 10489976
2025-07-28 15:30:18 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10489976)
2025-07-28 15:30:18 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:30:18 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10489976
2025-07-28 15:30:18 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10489976)
2025-07-28 15:30:18 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:30:18 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 15:30:18 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:30:18 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10489976
2025-07-28 15:30:18 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10489976)
2025-07-28 15:30:18 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:30:21 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:30:21 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 10489976, 新父窗口: 1776374
2025-07-28 15:30:21 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:30:21 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:30:21 [INFO] App_WorkbookActivate: 工作簿 '2025年高铁问题管理表梅汕高铁(1).xlsx' 激活处理完成
2025-07-28 15:30:21 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:30:21 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1776374)
2025-07-28 15:30:21 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:30:21 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 15:30:22 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:30:22 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:30:22 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:30:22 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:30:22 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 15:30:22 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:30:22 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:30:22 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:30:22 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:31:48 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:31:48 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10489976
2025-07-28 15:31:48 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10489976)
2025-07-28 15:31:48 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:31:48 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 15:31:48 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:31:48 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10489976)
2025-07-28 15:31:48 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:31:48 [INFO] App_WorkbookActivate: 工作簿 '2025年高铁问题管理表梅汕高铁.xlsx' 激活处理完成
2025-07-28 15:31:48 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:31:48 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10489976)
2025-07-28 15:31:48 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:31:48 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 15:31:48 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:31:48 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10489976
2025-07-28 15:31:48 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10489976)
2025-07-28 15:31:48 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:31:48 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 15:31:49 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:31:49 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10489976
2025-07-28 15:31:49 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10489976)
2025-07-28 15:31:49 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:32:03 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:32:03 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:32:03 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:32:03 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:32:03 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 15:32:03 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:32:03 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1776374)
2025-07-28 15:32:03 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:32:03 [INFO] App_WorkbookActivate: 工作簿 '2025年高铁问题管理表梅汕高铁(1).xlsx' 激活处理完成
2025-07-28 15:32:03 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:32:03 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1776374)
2025-07-28 15:32:03 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:32:03 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 15:32:03 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:32:03 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:32:03 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:32:03 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:32:03 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 15:32:03 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:32:04 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:32:04 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:32:04 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:32:24 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:32:24 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10489976
2025-07-28 15:32:24 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10489976)
2025-07-28 15:32:24 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:32:24 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 15:32:24 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:32:24 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10489976)
2025-07-28 15:32:24 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:32:24 [INFO] App_WorkbookActivate: 工作簿 '2025年高铁问题管理表梅汕高铁.xlsx' 激活处理完成
2025-07-28 15:32:24 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:32:24 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10489976)
2025-07-28 15:32:24 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:32:24 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 15:32:24 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:32:24 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10489976
2025-07-28 15:32:24 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10489976)
2025-07-28 15:32:24 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:32:24 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 15:32:25 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:32:25 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10489976
2025-07-28 15:32:25 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10489976)
2025-07-28 15:32:25 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:32:26 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:32:26 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10489976
2025-07-28 15:32:26 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10489976)
2025-07-28 15:32:26 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:32:26 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 15:32:32 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:32:32 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:32:32 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:32:32 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:32:32 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 15:32:33 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:32:33 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1776374)
2025-07-28 15:32:33 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:32:33 [INFO] App_WorkbookActivate: 工作簿 '2025年高铁问题管理表梅汕高铁(1).xlsx' 激活处理完成
2025-07-28 15:32:33 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:32:33 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1776374)
2025-07-28 15:32:33 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:32:33 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 15:32:33 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:32:33 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:32:33 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:32:33 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:32:33 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 15:32:33 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:32:33 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:32:33 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:32:33 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:32:34 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:32:34 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:32:34 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:32:34 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:32:34 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 15:32:34 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:32:34 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:32:34 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:32:34 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:32:34 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 15:32:50 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:32:51 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:32:51 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:32:51 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:32:51 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 15:32:58 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:32:58 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:32:58 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:32:58 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:32:58 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 15:35:41 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:35:41 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:35:41 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:35:41 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:35:41 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 15:35:44 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:35:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:35:44 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:35:44 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:35:44 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 15:38:16 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:38:16 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10489976
2025-07-28 15:38:16 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10489976)
2025-07-28 15:38:16 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:38:16 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 15:38:17 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:38:17 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10489976)
2025-07-28 15:38:17 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:38:17 [INFO] App_WorkbookActivate: 工作簿 '2025年高铁问题管理表梅汕高铁.xlsx' 激活处理完成
2025-07-28 15:38:17 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:38:17 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10489976)
2025-07-28 15:38:17 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:38:17 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 15:38:17 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:38:17 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10489976
2025-07-28 15:38:17 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10489976)
2025-07-28 15:38:17 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:38:17 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 15:38:17 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:38:17 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10489976
2025-07-28 15:38:17 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10489976)
2025-07-28 15:38:17 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:38:20 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:38:20 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10489976
2025-07-28 15:38:20 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10489976)
2025-07-28 15:38:20 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:38:20 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 15:38:21 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-28 15:38:21 [INFO] 系统事件监控已停止
2025-07-28 15:38:21 [INFO] Excel窗口句柄监控已停止
2025-07-28 15:38:21 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-28 15:38:21 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:38:21 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:38:21 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:38:21 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 1776374
2025-07-28 15:38:21 [INFO] 系统事件监控已启动
2025-07-28 15:38:21 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:38:21 [INFO] App_WorkbookActivate: 工作簿 '2025年高铁问题管理表梅汕高铁(1).xlsx' 激活处理完成
2025-07-28 15:38:21 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:38:21 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1776374)
2025-07-28 15:38:21 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:38:21 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 15:38:21 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:38:21 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:38:21 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:38:21 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:38:21 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 15:38:21 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:38:21 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:38:21 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:38:21 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:43:17 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:43:17 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:43:17 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:43:17 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:43:17 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 15:56:54 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:56:54 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6816868
2025-07-28 15:56:54 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 15:56:54 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:56:54 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 15:56:55 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:56:55 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 6816868)
2025-07-28 15:56:55 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:56:55 [INFO] App_WorkbookActivate: 工作簿 '★铁塔会审.xlsx' 激活处理完成
2025-07-28 15:56:55 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:56:55 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 6816868)
2025-07-28 15:56:55 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:56:55 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 15:56:55 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:56:55 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6816868
2025-07-28 15:56:55 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 15:56:55 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:56:55 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 15:56:55 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:56:55 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6816868
2025-07-28 15:56:55 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 15:56:55 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:57:34 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:57:34 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 6816868, 新父窗口: 1776374
2025-07-28 15:57:34 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:57:34 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:57:34 [INFO] App_WorkbookActivate: 工作簿 '2025年高铁问题管理表梅汕高铁(1).xlsx' 激活处理完成
2025-07-28 15:57:34 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:57:34 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1776374)
2025-07-28 15:57:34 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:57:34 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 15:57:35 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:57:35 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:57:35 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:57:35 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:57:35 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 15:57:35 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:57:35 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:57:35 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:57:35 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:57:36 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:57:36 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 15:57:36 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 15:57:36 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:57:36 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 15:58:43 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:58:43 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 1776374, 新父窗口: 6816868
2025-07-28 15:58:43 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 15:58:43 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:58:43 [INFO] App_WorkbookActivate: 工作簿 '★铁塔会审.xlsx' 激活处理完成
2025-07-28 15:58:43 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 15:58:43 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 6816868)
2025-07-28 15:58:43 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 15:58:43 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 15:58:44 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:58:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6816868
2025-07-28 15:58:44 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 15:58:44 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 15:58:44 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 15:58:44 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 15:58:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6816868
2025-07-28 15:58:44 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 15:58:44 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:02:48 [INFO] App_WorkbookOpen: 工作簿 '提需求-【3.5铁塔初步评审】揭阳0305(已经按这个购买天线，已考虑预留)-20250711.xlsx' 打开事件触发
2025-07-28 16:02:48 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:02:48 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 6816868, 新父窗口: 5247944
2025-07-28 16:02:48 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 5247944)
2025-07-28 16:02:48 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:02:48 [INFO] App_WorkbookOpen: 工作簿 '提需求-【3.5铁塔初步评审】揭阳0305(已经按这个购买天线，已考虑预留)-20250711.xlsx' 打开处理完成
2025-07-28 16:02:49 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:02:49 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 5247944)
2025-07-28 16:02:49 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:02:49 [INFO] App_WorkbookActivate: 工作簿 '提需求-【3.5铁塔初步评审】揭阳0305(已经按这个购买天线，已考虑预留)-20250711.xlsx' 激活处理完成
2025-07-28 16:02:49 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:02:49 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 5247944)
2025-07-28 16:02:49 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:02:49 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 16:02:49 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:02:49 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:02:49 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 5247944
2025-07-28 16:02:49 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 5247944)
2025-07-28 16:02:49 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:02:49 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 16:02:49 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 5247944)
2025-07-28 16:02:49 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:02:49 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:02:49 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 5247944
2025-07-28 16:02:49 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 5247944)
2025-07-28 16:02:49 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:02:49 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-28 16:03:06 [INFO] OpenForm: 准备打开窗体 '复制及合并'，位置: Center，单实例: True
2025-07-28 16:03:06 [INFO] 开始显示窗体 '复制及合并'，位置模式: Center
2025-07-28 16:03:06 [INFO] 窗体 '复制及合并' 以TopMostForm为父窗体显示
2025-07-28 16:03:06 [INFO] 窗体 '复制及合并' 显示完成，句柄: 9640166
2025-07-28 16:03:06 [INFO] OpenForm: 窗体 '复制及合并' 打开成功
2025-07-28 16:04:24 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:04:24 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 5247944, 新父窗口: 6816868
2025-07-28 16:04:24 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 16:04:24 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:04:24 [INFO] App_WorkbookActivate: 工作簿 '★铁塔会审.xlsx' 激活处理完成
2025-07-28 16:04:24 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:04:24 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 6816868)
2025-07-28 16:04:24 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:04:24 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 16:04:24 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:04:25 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6816868
2025-07-28 16:04:25 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 16:04:25 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:04:25 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 16:04:25 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:04:25 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6816868
2025-07-28 16:04:25 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 16:04:25 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:04:33 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:04:33 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 6816868, 新父窗口: 5247944
2025-07-28 16:04:33 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 5247944)
2025-07-28 16:04:33 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:04:33 [INFO] App_WorkbookActivate: 工作簿 '提需求-【3.5铁塔初步评审】揭阳0305(已经按这个购买天线，已考虑预留)-20250711.xlsx' 激活处理完成
2025-07-28 16:04:33 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:04:33 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 5247944)
2025-07-28 16:04:33 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:04:33 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 16:04:34 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:04:34 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 5247944
2025-07-28 16:04:34 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 5247944)
2025-07-28 16:04:34 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:04:34 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 16:04:34 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:04:34 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 5247944
2025-07-28 16:04:34 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 5247944)
2025-07-28 16:04:34 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:10:44 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:10:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 5247944, 新父窗口: 1776374
2025-07-28 16:10:44 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 16:10:44 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:10:44 [INFO] App_WorkbookActivate: 工作簿 '2025年高铁问题管理表梅汕高铁(1).xlsx' 激活处理完成
2025-07-28 16:10:44 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:10:44 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1776374)
2025-07-28 16:10:44 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:10:44 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 16:10:44 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:10:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 16:10:44 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 16:10:44 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:10:44 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 16:10:44 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:10:44 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:10:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6816868
2025-07-28 16:10:44 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 16:10:44 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:10:44 [INFO] App_WorkbookActivate: 工作簿 '★铁塔会审.xlsx' 激活处理完成
2025-07-28 16:10:44 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:10:44 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 6816868)
2025-07-28 16:10:44 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:10:44 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 16:10:45 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 6816868)
2025-07-28 16:10:45 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:10:45 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:10:45 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6816868
2025-07-28 16:10:45 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 16:10:45 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:10:45 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 16:10:45 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:10:45 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6816868
2025-07-28 16:10:45 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 16:10:45 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:10:51 [INFO] OpenForm: 准备打开窗体 '复制及合并'，位置: Center，单实例: True
2025-07-28 16:10:51 [INFO] 开始显示窗体 '复制及合并'，位置模式: Center
2025-07-28 16:10:51 [INFO] 窗体 '复制及合并' 以TopMostForm为父窗体显示
2025-07-28 16:10:51 [INFO] 窗体 '复制及合并' 显示完成，句柄: 5903096
2025-07-28 16:10:51 [INFO] OpenForm: 窗体 '复制及合并' 打开成功
2025-07-28 16:16:23 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:16:23 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 6816868, 新父窗口: 1776374
2025-07-28 16:16:23 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 16:16:23 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:16:23 [INFO] App_WorkbookActivate: 工作簿 '2025年高铁问题管理表梅汕高铁(1).xlsx' 激活处理完成
2025-07-28 16:16:23 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:16:23 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1776374)
2025-07-28 16:16:23 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:16:23 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 16:16:23 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:16:23 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 16:16:23 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 16:16:23 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:16:23 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 16:16:23 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:16:23 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 16:16:23 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 16:16:23 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:16:58 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-28 16:16:58 [INFO] 系统事件监控已停止
2025-07-28 16:16:58 [INFO] Excel窗口句柄监控已停止
2025-07-28 16:16:58 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-28 16:17:22 [INFO] OpenForm: 准备打开窗体 '备份及发送'，位置: Center，单实例: True
2025-07-28 16:17:22 [INFO] 开始显示窗体 '备份及发送'，位置模式: Center
2025-07-28 16:17:22 [INFO] 窗体 '备份及发送' 以TopMostForm为父窗体显示
2025-07-28 16:17:22 [INFO] 窗体 '备份及发送' 显示完成，句柄: 4722756
2025-07-28 16:17:22 [INFO] OpenForm: 窗体 '备份及发送' 打开成功
2025-07-28 16:41:48 [INFO] App_WorkbookOpen: 工作簿 '2025年高铁问题管理表梅汕高铁-揭阳补点需求15个.xlsx' 打开事件触发
2025-07-28 16:41:48 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:41:48 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4002264
2025-07-28 16:41:48 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4002264)
2025-07-28 16:41:48 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 4002264
2025-07-28 16:41:48 [INFO] 系统事件监控已启动
2025-07-28 16:41:48 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:41:48 [INFO] App_WorkbookOpen: 工作簿 '2025年高铁问题管理表梅汕高铁-揭阳补点需求15个.xlsx' 打开处理完成
2025-07-28 16:41:48 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:41:48 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4002264)
2025-07-28 16:41:48 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:41:48 [INFO] App_WorkbookActivate: 工作簿 '2025年高铁问题管理表梅汕高铁-揭阳补点需求15个.xlsx' 激活处理完成
2025-07-28 16:41:48 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:41:48 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4002264)
2025-07-28 16:41:48 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:41:48 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 16:41:49 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:41:49 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4002264
2025-07-28 16:41:49 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4002264)
2025-07-28 16:41:49 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:41:49 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 16:41:49 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:41:49 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4002264
2025-07-28 16:41:49 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4002264)
2025-07-28 16:41:49 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:41:49 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:41:49 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4002264
2025-07-28 16:41:49 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4002264)
2025-07-28 16:41:49 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:41:49 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-28 16:41:54 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-28 16:41:54 [INFO] 系统事件监控已停止
2025-07-28 16:41:54 [INFO] Excel窗口句柄监控已停止
2025-07-28 16:41:54 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-28 16:41:54 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:41:54 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 16:41:54 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 16:41:54 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 1776374
2025-07-28 16:41:54 [INFO] 系统事件监控已启动
2025-07-28 16:41:54 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:41:54 [INFO] App_WorkbookActivate: 工作簿 '2025年高铁问题管理表梅汕高铁(1).xlsx' 激活处理完成
2025-07-28 16:41:54 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:41:54 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1776374)
2025-07-28 16:41:54 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:41:54 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 16:41:54 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:41:54 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 16:41:54 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 16:41:54 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:41:54 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 16:41:54 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:41:54 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 16:41:54 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 16:41:54 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:41:55 [INFO] App_WorkbookOpen: 工作簿 '2025年高铁问题管理表梅汕高铁-揭阳补点需求15个.xlsx' 打开事件触发
2025-07-28 16:41:55 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:41:55 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 1776374, 新父窗口: 3283778
2025-07-28 16:41:55 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3283778)
2025-07-28 16:41:55 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:41:55 [INFO] App_WorkbookOpen: 工作簿 '2025年高铁问题管理表梅汕高铁-揭阳补点需求15个.xlsx' 打开处理完成
2025-07-28 16:41:56 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:41:56 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 3283778)
2025-07-28 16:41:56 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:41:56 [INFO] App_WorkbookActivate: 工作簿 '2025年高铁问题管理表梅汕高铁-揭阳补点需求15个.xlsx' 激活处理完成
2025-07-28 16:41:56 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 16:41:56 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 3283778)
2025-07-28 16:41:56 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 16:41:56 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 16:41:56 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:41:56 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3283778
2025-07-28 16:41:56 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3283778)
2025-07-28 16:41:56 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:41:56 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 16:41:56 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:41:56 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3283778
2025-07-28 16:41:56 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3283778)
2025-07-28 16:41:56 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:41:56 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 16:41:56 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3283778
2025-07-28 16:41:56 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3283778)
2025-07-28 16:41:56 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 16:41:56 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-28 17:39:10 [INFO] App_WorkbookOpen: 工作簿 '2CC扩容清单-揭阳-【确认的中兴2CC扩容32TR64TR清单】-20250715V3.1(1)(1).xlsx' 打开事件触发
2025-07-28 17:39:10 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 17:39:10 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 3283778, 新父窗口: 3736810
2025-07-28 17:39:10 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3736810)
2025-07-28 17:39:10 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 17:39:10 [INFO] App_WorkbookOpen: 工作簿 '2CC扩容清单-揭阳-【确认的中兴2CC扩容32TR64TR清单】-20250715V3.1(1)(1).xlsx' 打开处理完成
2025-07-28 17:39:10 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 17:39:10 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 3736810)
2025-07-28 17:39:10 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 17:39:10 [INFO] App_WorkbookActivate: 工作簿 '2CC扩容清单-揭阳-【确认的中兴2CC扩容32TR64TR清单】-20250715V3.1(1)(1).xlsx' 激活处理完成
2025-07-28 17:39:10 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 17:39:10 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 3736810)
2025-07-28 17:39:10 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 17:39:10 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 17:39:10 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 17:39:10 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3736810
2025-07-28 17:39:10 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3736810)
2025-07-28 17:39:10 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 17:39:10 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 17:39:10 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 17:39:10 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3736810
2025-07-28 17:39:10 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3736810)
2025-07-28 17:39:10 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 17:39:11 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 17:39:11 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3736810
2025-07-28 17:39:11 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3736810)
2025-07-28 17:39:11 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 17:39:11 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-28 17:40:09 [WARN] 检测到Excel窗口句柄变化: 3736810 -> 1645458
2025-07-28 17:40:09 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 3736810, 新父窗口: 1645458
2025-07-28 17:40:09 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1645458)
2025-07-28 17:40:15 [INFO] App_WorkbookOpen: 工作簿 '2025年无线网5GA工程管控表0727.xlsx' 打开事件触发
2025-07-28 17:40:15 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 17:40:15 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1645458)
2025-07-28 17:40:15 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 17:40:15 [INFO] App_WorkbookOpen: 工作簿 '2025年无线网5GA工程管控表0727.xlsx' 打开处理完成
2025-07-28 17:40:15 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 17:40:15 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1645458)
2025-07-28 17:40:15 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 17:40:15 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 17:40:15 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8001462
2025-07-28 17:40:15 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8001462)
2025-07-28 17:40:15 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 17:40:15 [INFO] App_WorkbookActivate: 工作簿 '2025年无线网5GA工程管控表0727.xlsx' 激活处理完成
2025-07-28 17:40:15 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 17:40:15 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 8001462)
2025-07-28 17:40:15 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 17:40:15 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 17:40:15 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 17:40:15 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 17:40:15 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 17:40:16 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8001462
2025-07-28 17:40:16 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 8001462)
2025-07-28 17:40:16 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8001462)
2025-07-28 17:40:16 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 8001462)
2025-07-28 17:40:16 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 17:40:16 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 17:40:16 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 17:40:16 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-28 17:40:16 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 17:40:31 [INFO] OpenForm: 准备打开窗体 '复制及合并'，位置: Center，单实例: True
2025-07-28 17:40:31 [INFO] 开始显示窗体 '复制及合并'，位置模式: Center
2025-07-28 17:40:31 [INFO] 窗体 '复制及合并' 以TopMostForm为父窗体显示
2025-07-28 17:40:31 [INFO] 窗体 '复制及合并' 显示完成，句柄: 3607738
2025-07-28 17:40:31 [INFO] OpenForm: 窗体 '复制及合并' 打开成功
2025-07-28 17:40:35 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 17:40:35 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 8001462, 新父窗口: 3736810
2025-07-28 17:40:35 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3736810)
2025-07-28 17:40:35 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 17:40:35 [INFO] App_WorkbookActivate: 工作簿 '2CC扩容清单-揭阳-【确认的中兴2CC扩容32TR64TR清单】-20250715V3.1(1)(1).xlsx' 激活处理完成
2025-07-28 17:40:35 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 17:40:35 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 3736810)
2025-07-28 17:40:35 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 17:40:35 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 17:40:35 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 17:40:35 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3736810
2025-07-28 17:40:35 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3736810)
2025-07-28 17:40:35 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 17:40:35 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 17:40:35 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 17:40:35 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3736810
2025-07-28 17:40:35 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3736810)
2025-07-28 17:40:35 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 17:40:39 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-28 17:40:40 [INFO] OpenForm: 准备打开窗体 '查找站点'，位置: Center，单实例: False
2025-07-28 17:40:40 [INFO] 开始显示窗体 '查找站点'，位置模式: Center
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：'2CC扩容清单'!$H:$H
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：'2CC扩容清单'!$H:$H，触发事件：True
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：'2CC扩容清单'!$I:$J
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：'2CC扩容清单'!$I:$J，触发事件：True
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：'2CC扩容清单'!$H:$H
2025-07-28 17:40:40 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：'2CC扩容清单'!$H:$H，触发事件：True
2025-07-28 17:40:40 [INFO] 窗体 '查找站点' 以TopMostForm为父窗体显示
2025-07-28 17:40:40 [INFO] 窗体 '查找站点' 显示完成，句柄: 12714602
2025-07-28 17:40:40 [INFO] OpenForm: 窗体 '查找站点' 打开成功
2025-07-28 17:40:46 [INFO] OpenForm: 准备打开窗体 '批量查找'，位置: Right，单实例: True
2025-07-28 17:40:46 [INFO] 开始显示窗体 '批量查找'，位置模式: Right
2025-07-28 17:40:46 [INFO] 窗体 '批量查找' 以TopMostForm为父窗体显示
2025-07-28 17:40:46 [INFO] 窗体 '批量查找' 显示完成，句柄: 13765818
2025-07-28 17:40:46 [INFO] OpenForm: 窗体 '批量查找' 打开成功
2025-07-28 18:21:19 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-28 18:21:19 [INFO] 系统事件监控已停止
2025-07-28 18:21:19 [INFO] Excel窗口句柄监控已停止
2025-07-28 18:21:19 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-28 18:21:20 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:21:20 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8001462
2025-07-28 18:21:20 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8001462)
2025-07-28 18:21:20 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 8001462
2025-07-28 18:21:20 [INFO] 系统事件监控已启动
2025-07-28 18:21:20 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:21:20 [INFO] App_WorkbookActivate: 工作簿 '2025年无线网5GA工程管控表0727.xlsx' 激活处理完成
2025-07-28 18:21:20 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:21:20 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 8001462)
2025-07-28 18:21:20 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:21:20 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 18:21:21 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:21:21 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8001462
2025-07-28 18:21:21 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8001462)
2025-07-28 18:21:21 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:21:21 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 18:21:21 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:21:21 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8001462
2025-07-28 18:21:21 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8001462)
2025-07-28 18:21:21 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:22:10 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:22:10 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 8001462, 新父窗口: 5247944
2025-07-28 18:22:10 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 5247944)
2025-07-28 18:22:10 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:22:10 [INFO] App_WorkbookActivate: 工作簿 '提需求-【3.5铁塔初步评审】揭阳0305(已经按这个购买天线，已考虑预留)-20250711.xlsx' 激活处理完成
2025-07-28 18:22:10 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:22:10 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 5247944)
2025-07-28 18:22:10 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:22:10 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 18:22:10 [WARN] 检测到Excel窗口句柄变化: 8001462 -> 5247944
2025-07-28 18:22:10 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 5247944)
2025-07-28 18:22:10 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:22:11 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:22:11 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 5247944
2025-07-28 18:22:11 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 5247944)
2025-07-28 18:22:11 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:22:11 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 18:22:11 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 5247944)
2025-07-28 18:22:11 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:22:11 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-28 18:22:11 [INFO] 系统事件监控已停止
2025-07-28 18:22:11 [INFO] Excel窗口句柄监控已停止
2025-07-28 18:22:11 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-28 18:22:12 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:22:12 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8001462
2025-07-28 18:22:12 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8001462)
2025-07-28 18:22:12 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 8001462
2025-07-28 18:22:12 [INFO] 系统事件监控已启动
2025-07-28 18:22:12 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:22:12 [INFO] App_WorkbookActivate: 工作簿 '2025年无线网5GA工程管控表0727.xlsx' 激活处理完成
2025-07-28 18:22:12 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:22:12 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 8001462)
2025-07-28 18:22:12 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:22:12 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 18:22:13 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:22:13 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:22:13 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3283778
2025-07-28 18:22:13 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3283778)
2025-07-28 18:22:13 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:22:13 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 3283778)
2025-07-28 18:22:13 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:22:13 [INFO] App_WorkbookActivate: 工作簿 '2025年高铁问题管理表梅汕高铁-揭阳补点需求15个.xlsx' 激活处理完成
2025-07-28 18:22:13 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:22:13 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 3283778)
2025-07-28 18:22:13 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:22:13 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 18:22:13 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:22:13 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 18:22:13 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 3283778)
2025-07-28 18:22:13 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:22:13 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:22:13 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3283778
2025-07-28 18:22:13 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3283778)
2025-07-28 18:22:13 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:22:13 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 18:22:13 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:22:13 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3283778
2025-07-28 18:22:14 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3283778)
2025-07-28 18:22:14 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:22:14 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-28 18:22:14 [INFO] 系统事件监控已停止
2025-07-28 18:22:14 [INFO] Excel窗口句柄监控已停止
2025-07-28 18:22:14 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-28 18:22:15 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:22:15 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8001462
2025-07-28 18:22:15 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8001462)
2025-07-28 18:22:15 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 8001462
2025-07-28 18:22:15 [INFO] 系统事件监控已启动
2025-07-28 18:22:15 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:22:15 [INFO] App_WorkbookActivate: 工作簿 '2025年无线网5GA工程管控表0727.xlsx' 激活处理完成
2025-07-28 18:22:15 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:22:15 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 8001462)
2025-07-28 18:22:15 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:22:15 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 18:22:15 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:22:16 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:22:16 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8001462
2025-07-28 18:22:16 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8001462)
2025-07-28 18:22:16 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:22:16 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 18:22:16 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 8001462)
2025-07-28 18:22:16 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:22:17 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:22:17 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 8001462, 新父窗口: 6816868
2025-07-28 18:22:17 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 18:22:17 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:22:17 [INFO] App_WorkbookActivate: 工作簿 '★铁塔会审.xlsx' 激活处理完成
2025-07-28 18:22:17 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:22:17 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 6816868)
2025-07-28 18:22:17 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:22:17 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 18:22:18 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:22:18 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6816868
2025-07-28 18:22:18 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 18:22:18 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:22:18 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 18:22:18 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:22:18 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6816868
2025-07-28 18:22:18 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6816868)
2025-07-28 18:22:18 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:22:18 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-28 18:22:18 [INFO] 系统事件监控已停止
2025-07-28 18:22:18 [INFO] Excel窗口句柄监控已停止
2025-07-28 18:22:18 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-28 18:22:20 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:22:20 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8001462
2025-07-28 18:22:20 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8001462)
2025-07-28 18:22:20 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 8001462
2025-07-28 18:22:20 [INFO] 系统事件监控已启动
2025-07-28 18:22:20 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:22:20 [INFO] App_WorkbookActivate: 工作簿 '2025年无线网5GA工程管控表0727.xlsx' 激活处理完成
2025-07-28 18:22:20 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:22:20 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 8001462)
2025-07-28 18:22:20 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:22:20 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 18:22:20 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:22:20 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8001462
2025-07-28 18:22:20 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8001462)
2025-07-28 18:22:20 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:22:20 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 18:22:20 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:22:21 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8001462
2025-07-28 18:22:21 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8001462)
2025-07-28 18:22:21 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:22:21 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:22:21 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 8001462, 新父窗口: 1776374
2025-07-28 18:22:21 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 18:22:21 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:22:21 [INFO] App_WorkbookActivate: 工作簿 '2025年高铁问题管理表梅汕高铁(1).xlsx' 激活处理完成
2025-07-28 18:22:21 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:22:21 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1776374)
2025-07-28 18:22:21 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:22:21 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 18:22:21 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:22:21 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 18:22:21 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 18:22:21 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:22:21 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 18:22:21 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:22:21 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1776374
2025-07-28 18:22:21 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1776374)
2025-07-28 18:22:21 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:22:22 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-28 18:22:22 [INFO] 系统事件监控已停止
2025-07-28 18:22:22 [INFO] Excel窗口句柄监控已停止
2025-07-28 18:22:22 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-28 18:22:23 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:22:23 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8001462
2025-07-28 18:22:23 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8001462)
2025-07-28 18:22:23 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 8001462
2025-07-28 18:22:23 [INFO] 系统事件监控已启动
2025-07-28 18:22:23 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:22:23 [INFO] App_WorkbookActivate: 工作簿 '2025年无线网5GA工程管控表0727.xlsx' 激活处理完成
2025-07-28 18:22:23 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:22:23 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 8001462)
2025-07-28 18:22:23 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:22:23 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 18:22:23 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:22:23 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8001462
2025-07-28 18:22:23 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8001462)
2025-07-28 18:22:23 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:22:23 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 18:22:23 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:22:23 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8001462
2025-07-28 18:22:23 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8001462)
2025-07-28 18:22:23 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:22:24 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-28 18:22:24 [INFO] 系统事件监控已停止
2025-07-28 18:22:24 [INFO] Excel窗口句柄监控已停止
2025-07-28 18:22:24 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-28 18:22:27 [INFO] 开始VSTO插件关闭流程
2025-07-28 18:22:27 [INFO] 程序集追踪日志已保存到: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\logs\AssemblyTrace_20250728_182227.txt
2025-07-28 18:22:27 [INFO] VSTO插件关闭流程完成
2025-07-28 18:22:45 [INFO] Excel窗口句柄监控器初始化完成
2025-07-28 18:22:45 [INFO] 配置文件实例已在加载时初始化
2025-07-28 18:22:45 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-07-28 18:22:45 [INFO] 🔍 === 直接测试znAbout控件状态 ===
2025-07-28 18:22:45 [INFO] 🔍 znAbout控件实例: 存在
2025-07-28 18:22:45 [INFO] 🔍 znAbout.Label: 'ZnAbout'
2025-07-28 18:22:45 [INFO] 🔍 znAbout.Name: 'znAbout'
2025-07-28 18:22:45 [INFO] 🔍 znAbout类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-28 18:22:45 [INFO] 🔍 znAboutGroup控件实例: 存在
2025-07-28 18:22:45 [INFO] 🔍 znAboutGroup.Label: '授权'
2025-07-28 18:22:45 [INFO] 🔍 znAboutGroup.Name: 'znAboutGroup'
2025-07-28 18:22:45 [INFO] 🔍 znAboutGroup类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-28 18:22:45 [INFO] 🔍 znAboutButton控件实例: 存在
2025-07-28 18:22:45 [INFO] 🔍 znAboutButton.Label: '授权'
2025-07-28 18:22:45 [INFO] 🔍 znAboutButton.Name: 'znAboutButton'
2025-07-28 18:22:45 [INFO] 🔍 znAboutButton类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-28 18:22:45 [INFO] 🔍 === znAbout控件状态测试完成 ===
2025-07-28 18:22:45 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-07-28 18:22:45 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-07-28 18:22:45 [INFO] 成功初始化Excel应用程序实例
2025-07-28 18:22:45 [INFO] 自动备份路径未配置
2025-07-28 18:22:45 [DEBUG] 开始初始化授权控制器
2025-07-28 18:22:46 [DEBUG] 授权系统初始化完成，耗时: 417ms
2025-07-28 18:22:46 [DEBUG] 开始初始化授权验证
2025-07-28 18:22:46 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-07-28 18:22:46 [DEBUG] 权限管理器初始化成功
2025-07-28 18:22:46 [DEBUG] 使用新的权限管理器进行初始化
2025-07-28 18:22:46 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-28 18:22:46 [INFO] 开始初始化UI权限管理
2025-07-28 18:22:46 [DEBUG] [实例ID: 24f88be4] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-28 18:22:46 [DEBUG] 🔍 [实例ID: 24f88be4] 字典引用一致性检查:
2025-07-28 18:22:46 [DEBUG] 🔍   标题映射一致性: True
2025-07-28 18:22:46 [DEBUG] 🔍   权限映射一致性: True
2025-07-28 18:22:46 [DEBUG] 🔍   信息映射一致性: True
2025-07-28 18:22:46 [DEBUG] 🔍   特殊控件一致性: True
2025-07-28 18:22:46 [DEBUG] 控件权限管理器初始化完成 [实例ID: 24f88be4]
2025-07-28 18:22:46 [DEBUG] 开始注册控件权限映射
2025-07-28 18:22:46 [INFO] 开始初始化全局控件映射
2025-07-28 18:22:46 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-07-28 18:22:46 [DEBUG] 开始生成控件标题映射
2025-07-28 18:22:46 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-28 18:22:46 [DEBUG] 通过反射获取到 112 个字段
2025-07-28 18:22:46 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-28 18:22:46 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-28 18:22:46 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-28 18:22:46 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-28 18:22:46 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-28 18:22:46 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-28 18:22:46 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-28 18:22:46 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-07-28 18:22:46 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-07-28 18:22:46 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-07-28 18:22:46 [INFO] 控件标题映射生成完成，共生成 100 项映射
2025-07-28 18:22:46 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-28 18:22:46 [DEBUG] 全局控件标题映射生成完成，共生成 100 项
2025-07-28 18:22:46 [INFO] 关键控件标题映射: hyTab -> Develop
2025-07-28 18:22:46 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-07-28 18:22:46 [WARN] 关键控件未找到标题映射: buttonAbout
2025-07-28 18:22:46 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-07-28 18:22:46 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-07-28 18:22:46 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-07-28 18:22:46 [INFO] === znAbout控件标题映射诊断 ===
2025-07-28 18:22:46 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-07-28 18:22:46 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-07-28 18:22:46 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-07-28 18:22:46 [DEBUG] === 所有生成的控件标题映射 ===
2025-07-28 18:22:46 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-07-28 18:22:46 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-07-28 18:22:46 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-07-28 18:22:46 [DEBUG] 控件映射: btn发送及存档 -> '发送及存档'
2025-07-28 18:22:46 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-07-28 18:22:46 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-07-28 18:22:46 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-07-28 18:22:46 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-07-28 18:22:46 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-07-28 18:22:46 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规检查'
2025-07-28 18:22:46 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-07-28 18:22:46 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-07-28 18:22:46 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-07-28 18:22:46 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-07-28 18:22:46 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-07-28 18:22:46 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-07-28 18:22:46 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-07-28 18:22:46 [DEBUG] 控件映射: button14 -> '发送及存档'
2025-07-28 18:22:46 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-07-28 18:22:46 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-07-28 18:22:46 [DEBUG] 控件映射: button17 -> '向下填充'
2025-07-28 18:22:46 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-07-28 18:22:46 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-07-28 18:22:46 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-07-28 18:22:46 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-07-28 18:22:46 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-07-28 18:22:46 [DEBUG] 控件映射: button3 -> '关于'
2025-07-28 18:22:46 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-07-28 18:22:46 [DEBUG] 控件映射: button5 -> '文件管理'
2025-07-28 18:22:46 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-07-28 18:22:46 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-07-28 18:22:46 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-07-28 18:22:46 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-07-28 18:22:46 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-07-28 18:22:46 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-07-28 18:22:46 [DEBUG] 控件映射: button9 -> '文件快开'
2025-07-28 18:22:46 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-07-28 18:22:46 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-07-28 18:22:46 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-07-28 18:22:46 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-07-28 18:22:46 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-07-28 18:22:46 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-07-28 18:22:46 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-07-28 18:22:46 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-07-28 18:22:46 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-07-28 18:22:46 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-07-28 18:22:46 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-07-28 18:22:46 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-07-28 18:22:46 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-07-28 18:22:46 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-07-28 18:22:46 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-07-28 18:22:46 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-07-28 18:22:46 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-07-28 18:22:46 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-07-28 18:22:46 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-07-28 18:22:46 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-07-28 18:22:46 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-07-28 18:22:46 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-07-28 18:22:46 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-07-28 18:22:46 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-07-28 18:22:46 [DEBUG] 控件映射: button文件操作 -> '文件操作'
2025-07-28 18:22:46 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-07-28 18:22:46 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-07-28 18:22:46 [DEBUG] 控件映射: button专用工具 -> '专用工具'
2025-07-28 18:22:46 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-07-28 18:22:46 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-07-28 18:22:46 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-07-28 18:22:46 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-07-28 18:22:46 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-07-28 18:22:46 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-07-28 18:22:46 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-07-28 18:22:46 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-07-28 18:22:46 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-07-28 18:22:46 [DEBUG] 控件映射: group1 -> '关于'
2025-07-28 18:22:46 [DEBUG] 控件映射: group2 -> '脚本'
2025-07-28 18:22:46 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-07-28 18:22:46 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-07-28 18:22:46 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-07-28 18:22:46 [DEBUG] 控件映射: group文件 -> '文件'
2025-07-28 18:22:46 [DEBUG] 控件映射: group无线 -> '无线'
2025-07-28 18:22:46 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-07-28 18:22:46 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-07-28 18:22:46 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-07-28 18:22:46 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-07-28 18:22:46 [DEBUG] 控件映射: menu1 -> '其它'
2025-07-28 18:22:46 [DEBUG] 控件映射: menu3 -> '设置'
2025-07-28 18:22:46 [DEBUG] 控件映射: menu5 -> '修复'
2025-07-28 18:22:46 [DEBUG] 控件映射: menuHY -> '其它'
2025-07-28 18:22:46 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-07-28 18:22:46 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-07-28 18:22:46 [DEBUG] 控件映射: menu修复 -> '修复'
2025-07-28 18:22:46 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-07-28 18:22:46 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-07-28 18:22:46 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-07-28 18:22:46 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-07-28 18:22:46 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-07-28 18:22:46 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-07-28 18:22:46 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-07-28 18:22:46 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-07-28 18:22:46 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-07-28 18:22:46 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-07-28 18:22:46 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-07-28 18:22:46 [DEBUG] 开始生成控件权限映射
2025-07-28 18:22:46 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-28 18:22:46 [DEBUG] 通过反射获取到 112 个字段
2025-07-28 18:22:46 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:22:46 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-28 18:22:46 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-28 18:22:46 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-28 18:22:46 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-28 18:22:46 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-28 18:22:46 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-28 18:22:46 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:22:46 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-28 18:22:46 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-28 18:22:46 [INFO] 控件权限映射生成完成，共生成 104 项映射
2025-07-28 18:22:46 [DEBUG] 全局控件权限映射生成完成，共生成 104 项
2025-07-28 18:22:46 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-07-28 18:22:46 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-07-28 18:22:46 [INFO] 全局控件映射初始化完成 - 标题映射: 100 项, 权限映射: 104 项
2025-07-28 18:22:46 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-28 18:22:46 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-28 18:22:46 [INFO] 开始初始化权限验证
2025-07-28 18:22:46 [DEBUG] 设置默认UI可见性为false
2025-07-28 18:22:46 [DEBUG] 开始检查所有需要的权限
2025-07-28 18:22:46 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-28 18:22:46 [INFO] 启动网络授权信息获取任务
2025-07-28 18:22:46 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-07-28 18:22:47 [INFO] 所有权限检查完成
2025-07-28 18:22:47 [DEBUG] 应用权限状态到UI控件
2025-07-28 18:22:47 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 18:22:47 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 18:22:47 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:22:47 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 18:22:47 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 18:22:47 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:22:47 [DEBUG] 已应用权限状态到UI控件
2025-07-28 18:22:47 [DEBUG] 启动后台权限刷新任务
2025-07-28 18:22:47 [DEBUG] 启动延迟权限刷新任务
2025-07-28 18:22:47 [INFO] 权限验证初始化完成
2025-07-28 18:22:47 [INFO] UI权限管理初始化完成
2025-07-28 18:22:47 [INFO] 收到权限管理器初始化完成通知
2025-07-28 18:22:47 [INFO] 开始刷新控件标题
2025-07-28 18:22:47 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 18:22:47 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 18:22:47 [DEBUG] 控件标题刷新完成
2025-07-28 18:22:47 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 18:22:47 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 18:22:47 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 18:22:47 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:22:47 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 18:22:47 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 18:22:47 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 18:22:47 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 18:22:47 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 18:22:47 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 18:22:47 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 18:22:47 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:22:47 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 18:22:47 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 18:22:47 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 18:22:47 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 18:22:47 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 18:22:47 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 18:22:47 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 18:22:47 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 18:22:47 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 18:22:47 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 18:22:47 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 18:22:47 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 18:22:47 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 18:22:47 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 18:22:47 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 18:22:47 [INFO] 控件标题更正完成
2025-07-28 18:22:47 [INFO] 控件标题刷新完成
2025-07-28 18:22:47 [INFO] 权限管理器初始化完成处理结束
2025-07-28 18:22:47 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-28 18:22:47 [DEBUG] 授权验证初始化完成
2025-07-28 18:22:47 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-07-28 18:22:47 [INFO] 成功加载配置和授权信息
2025-07-28 18:22:47 [INFO] 开始初始化定时器和设置
2025-07-28 18:22:47 [INFO] 定时器和设置初始化完成
2025-07-28 18:22:47 [INFO] 开始VSTO插件启动流程
2025-07-28 18:22:47 [INFO] TopMostForm窗体加载完成
2025-07-28 18:22:47 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:22:47 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 9376738
2025-07-28 18:22:47 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 9376738)
2025-07-28 18:22:47 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 9376738
2025-07-28 18:22:47 [INFO] 系统事件监控已启动
2025-07-28 18:22:47 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:22:47 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-07-28 18:22:47 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-07-28 18:22:47 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-07-28 18:22:47 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 9634486
2025-07-28 18:22:47 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-07-28 18:22:47 [INFO] VSTO插件启动流程完成
2025-07-28 18:22:47 [DEBUG] 开始重置 208 个命令栏
2025-07-28 18:22:47 [DEBUG] 重置命令栏: cell
2025-07-28 18:22:47 [DEBUG] 重置命令栏: column
2025-07-28 18:22:47 [DEBUG] 重置命令栏: row
2025-07-28 18:22:47 [DEBUG] 重置命令栏: cell
2025-07-28 18:22:47 [DEBUG] 重置命令栏: column
2025-07-28 18:22:47 [DEBUG] 重置命令栏: row
2025-07-28 18:22:47 [DEBUG] 重置命令栏: row
2025-07-28 18:22:47 [DEBUG] 重置命令栏: column
2025-07-28 18:22:48 [INFO] 从Remote成功获取到网络授权信息
2025-07-28 18:22:48 [INFO] 网络授权信息已更新并触发回调
2025-07-28 18:22:48 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-07-28 18:22:48 [INFO] 网络授权信息已从 Network 更新
2025-07-28 18:22:48 [INFO] 授权版本: 1.0
2025-07-28 18:22:48 [INFO] 颁发者: ExtensionsTools
2025-07-28 18:22:48 [INFO] 用户数量: 2
2025-07-28 18:22:48 [INFO] 分组权限数量: 2
2025-07-28 18:22:48 [WARN] 配置文件中未找到用户组信息
2025-07-28 18:22:48 [INFO] 已重新设置用户组: []
2025-07-28 18:22:48 [INFO] 用户组信息已重新设置
2025-07-28 18:22:48 [INFO] 立即刷新权限缓存和UI界面
2025-07-28 18:22:48 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-28 18:22:48 [DEBUG] 使用新的权限管理器进行强制刷新
2025-07-28 18:22:48 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-07-28 18:22:48 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-28 18:22:48 [DEBUG] 本地权限缓存已清空
2025-07-28 18:22:48 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-07-28 18:22:48 [INFO] 所有权限检查完成
2025-07-28 18:22:48 [DEBUG] 权限重新检查完成
2025-07-28 18:22:48 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 18:22:48 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:22:48 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 18:22:48 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:22:48 [DEBUG] 已应用权限状态到UI控件
2025-07-28 18:22:48 [INFO] UI界面权限状态已更新
2025-07-28 18:22:48 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 18:22:48 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 18:22:48 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-07-28 18:22:48 [INFO] 权限缓存和UI界面立即刷新完成
2025-07-28 18:22:48 [INFO] 网络授权已更新，开始刷新控件标题
2025-07-28 18:22:48 [INFO] 开始刷新Ribbon控件标题
2025-07-28 18:22:48 [DEBUG] 权限缓存已清空，清除了 104 个缓存项
2025-07-28 18:22:48 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-07-28 18:22:48 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 18:22:48 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 18:22:48 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 18:22:48 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:22:48 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 18:22:48 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 18:22:48 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 18:22:48 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 18:22:48 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 18:22:48 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 18:22:48 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 18:22:48 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:22:48 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 18:22:48 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 18:22:48 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 18:22:48 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 18:22:48 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:22:48 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 18:22:48 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:22:48 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 18:22:48 [DEBUG] 已应用权限状态到UI控件
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 18:22:48 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 18:22:48 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 18:22:48 [INFO] 控件标题更正完成
2025-07-28 18:22:48 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-07-28 18:22:48 [INFO] Ribbon控件标题刷新完成
2025-07-28 18:22:48 [INFO] 控件标题刷新完成
2025-07-28 18:22:48 [DEBUG] Ribbon控件标题已刷新
2025-07-28 18:22:48 [INFO] 开始刷新控件标题
2025-07-28 18:22:48 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 18:22:48 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 18:22:48 [DEBUG] 控件标题刷新完成
2025-07-28 18:22:48 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 18:22:48 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 18:22:48 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 18:22:48 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:22:48 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 18:22:48 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 18:22:48 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 18:22:48 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 18:22:48 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 18:22:48 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 18:22:48 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 18:22:48 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:22:48 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 18:22:48 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 18:22:48 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 18:22:48 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 18:22:48 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 18:22:48 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 18:22:48 [INFO] 控件标题更正完成
2025-07-28 18:22:48 [INFO] 控件标题刷新完成
2025-07-28 18:22:48 [DEBUG] Ribbon控件标题已立即刷新
2025-07-28 18:22:48 [INFO] 开始刷新授权状态
2025-07-28 18:22:48 [DEBUG] 开始初始化授权验证
2025-07-28 18:22:48 [DEBUG] 使用新的权限管理器进行初始化
2025-07-28 18:22:48 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-28 18:22:48 [INFO] 开始初始化UI权限管理
2025-07-28 18:22:48 [DEBUG] [实例ID: 1e15eeda] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-28 18:22:48 [DEBUG] 🔍 [实例ID: 1e15eeda] 字典引用一致性检查:
2025-07-28 18:22:48 [DEBUG] 🔍   标题映射一致性: True
2025-07-28 18:22:48 [DEBUG] 🔍   权限映射一致性: True
2025-07-28 18:22:48 [DEBUG] 🔍   信息映射一致性: True
2025-07-28 18:22:48 [DEBUG] 🔍   特殊控件一致性: True
2025-07-28 18:22:48 [DEBUG] 控件权限管理器初始化完成 [实例ID: 1e15eeda]
2025-07-28 18:22:48 [DEBUG] 开始注册控件权限映射
2025-07-28 18:22:48 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-28 18:22:48 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-28 18:22:48 [INFO] 开始初始化权限验证
2025-07-28 18:22:48 [DEBUG] 设置默认UI可见性为false
2025-07-28 18:22:48 [DEBUG] 开始检查所有需要的权限
2025-07-28 18:22:48 [INFO] 所有权限检查完成
2025-07-28 18:22:48 [DEBUG] 应用权限状态到UI控件
2025-07-28 18:22:48 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 18:22:48 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:22:48 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 18:22:48 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:22:48 [DEBUG] 已应用权限状态到UI控件
2025-07-28 18:22:48 [DEBUG] 启动后台权限刷新任务
2025-07-28 18:22:48 [DEBUG] 启动延迟权限刷新任务
2025-07-28 18:22:48 [INFO] 权限验证初始化完成
2025-07-28 18:22:48 [INFO] UI权限管理初始化完成
2025-07-28 18:22:48 [INFO] 收到权限管理器初始化完成通知
2025-07-28 18:22:48 [INFO] 开始刷新控件标题
2025-07-28 18:22:48 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 18:22:48 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 18:22:48 [DEBUG] 控件标题刷新完成
2025-07-28 18:22:48 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 18:22:48 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 18:22:48 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 18:22:48 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:22:48 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 18:22:48 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 18:22:48 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 18:22:48 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 18:22:48 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 18:22:48 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 18:22:48 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 18:22:48 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:22:48 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 18:22:48 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 18:22:48 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 18:22:48 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 18:22:48 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 18:22:48 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 18:22:48 [INFO] 控件标题更正完成
2025-07-28 18:22:48 [INFO] 控件标题刷新完成
2025-07-28 18:22:48 [INFO] 权限管理器初始化完成处理结束
2025-07-28 18:22:48 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-28 18:22:48 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 18:22:48 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 18:22:48 [DEBUG] 授权验证初始化完成
2025-07-28 18:22:48 [INFO] 授权状态刷新完成
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:22:48 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 18:22:48 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 18:22:48 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:22:48 [DEBUG] 已应用权限状态到UI控件
2025-07-28 18:22:49 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 18:22:49 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 18:22:49 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:22:49 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 18:22:49 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 18:22:49 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:22:49 [DEBUG] 已应用权限状态到UI控件
2025-07-28 18:22:49 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 18:22:49 [DEBUG] 授权控制器已初始化
2025-07-28 18:22:49 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 18:22:49 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:22:49 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 9376738
2025-07-28 18:22:49 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 9376738)
2025-07-28 18:22:49 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:22:49 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 18:22:50 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 18:22:50 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 18:22:50 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 18:22:50 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:22:50 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 18:22:50 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 18:22:50 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:22:50 [DEBUG] 已应用权限状态到UI控件
2025-07-28 18:22:51 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 18:22:51 [DEBUG] 授权控制器已初始化
2025-07-28 18:22:51 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 18:22:51 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 18:22:51 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 18:22:51 [DEBUG] 授权控制器已初始化
2025-07-28 18:22:51 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 18:22:52 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 18:22:52 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 18:22:52 [DEBUG] 授权控制器已初始化
2025-07-28 18:22:52 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 18:22:53 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 18:22:54 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 18:22:54 [DEBUG] 授权控制器已初始化
2025-07-28 18:22:54 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 18:22:55 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 18:22:55 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 18:22:55 [DEBUG] 授权控制器已初始化
2025-07-28 18:22:55 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 18:22:56 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 18:22:56 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 18:22:56 [DEBUG] 授权控制器已初始化
2025-07-28 18:22:56 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 18:22:57 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 18:22:57 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 18:22:57 [DEBUG] 授权控制器已初始化
2025-07-28 18:22:57 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 18:22:57 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 18:22:57 [DEBUG] 已重置工作表标签菜单
2025-07-28 18:22:57 [DEBUG] 工作表标签菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 18:23:01 [INFO] OpenForm: 准备打开窗体 '备份及发送'，位置: Center，单实例: True
2025-07-28 18:23:01 [INFO] 开始显示窗体 '备份及发送'，位置模式: Center
2025-07-28 18:23:01 [INFO] 窗体 '备份及发送' 以TopMostForm为父窗体显示
2025-07-28 18:23:01 [INFO] 窗体 '备份及发送' 显示完成，句柄: 3414138
2025-07-28 18:23:01 [INFO] OpenForm: 窗体 '备份及发送' 打开成功
2025-07-28 18:27:01 [INFO] OpenForm: 准备打开窗体 '备份及发送'，位置: Center，单实例: True
2025-07-28 18:27:01 [INFO] 开始显示窗体 '备份及发送'，位置模式: Center
2025-07-28 18:27:01 [INFO] 窗体 '备份及发送' 以TopMostForm为父窗体显示
2025-07-28 18:27:01 [INFO] 窗体 '备份及发送' 显示完成，句柄: 9310194
2025-07-28 18:27:01 [INFO] OpenForm: 窗体 '备份及发送' 打开成功
2025-07-28 18:27:26 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:27:26 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 9376738
2025-07-28 18:27:26 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 9376738)
2025-07-28 18:27:26 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:27:26 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 18:27:27 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:27:27 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 9376738
2025-07-28 18:27:27 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 9376738)
2025-07-28 18:27:27 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:27:27 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 18:27:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 18:27:29 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 18:27:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 18:27:29 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 18:27:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-28 18:27:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-28 18:27:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-28 18:27:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-28 18:27:29 [INFO] OpenForm: 准备打开窗体 'PPTHelper'，位置: Center，单实例: True
2025-07-28 18:27:29 [INFO] 开始显示窗体 'PPTHelper'，位置模式: Center
2025-07-28 18:32:38 [INFO] Excel窗口句柄监控器初始化完成
2025-07-28 18:32:39 [INFO] 配置文件实例已在加载时初始化
2025-07-28 18:32:39 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-07-28 18:32:39 [INFO] 🔍 === 直接测试znAbout控件状态 ===
2025-07-28 18:32:39 [INFO] 🔍 znAbout控件实例: 存在
2025-07-28 18:32:39 [INFO] 🔍 znAbout.Label: 'ZnAbout'
2025-07-28 18:32:39 [INFO] 🔍 znAbout.Name: 'znAbout'
2025-07-28 18:32:39 [INFO] 🔍 znAbout类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-28 18:32:39 [INFO] 🔍 znAboutGroup控件实例: 存在
2025-07-28 18:32:39 [INFO] 🔍 znAboutGroup.Label: '授权'
2025-07-28 18:32:39 [INFO] 🔍 znAboutGroup.Name: 'znAboutGroup'
2025-07-28 18:32:39 [INFO] 🔍 znAboutGroup类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-28 18:32:39 [INFO] 🔍 znAboutButton控件实例: 存在
2025-07-28 18:32:39 [INFO] 🔍 znAboutButton.Label: '授权'
2025-07-28 18:32:39 [INFO] 🔍 znAboutButton.Name: 'znAboutButton'
2025-07-28 18:32:39 [INFO] 🔍 znAboutButton类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-28 18:32:39 [INFO] 🔍 === znAbout控件状态测试完成 ===
2025-07-28 18:32:39 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-07-28 18:32:39 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-07-28 18:32:39 [INFO] 成功初始化Excel应用程序实例
2025-07-28 18:32:39 [INFO] 自动备份路径未配置
2025-07-28 18:32:39 [DEBUG] 开始初始化授权控制器
2025-07-28 18:32:39 [DEBUG] 授权系统初始化完成，耗时: 415ms
2025-07-28 18:32:39 [DEBUG] 开始初始化授权验证
2025-07-28 18:32:39 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-07-28 18:32:39 [DEBUG] 权限管理器初始化成功
2025-07-28 18:32:39 [DEBUG] 使用新的权限管理器进行初始化
2025-07-28 18:32:39 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-28 18:32:39 [INFO] 开始初始化UI权限管理
2025-07-28 18:32:39 [DEBUG] [实例ID: 0eff9be7] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-28 18:32:39 [DEBUG] 🔍 [实例ID: 0eff9be7] 字典引用一致性检查:
2025-07-28 18:32:39 [DEBUG] 🔍   标题映射一致性: True
2025-07-28 18:32:39 [DEBUG] 🔍   权限映射一致性: True
2025-07-28 18:32:39 [DEBUG] 🔍   信息映射一致性: True
2025-07-28 18:32:39 [DEBUG] 🔍   特殊控件一致性: True
2025-07-28 18:32:39 [DEBUG] 控件权限管理器初始化完成 [实例ID: 0eff9be7]
2025-07-28 18:32:39 [DEBUG] 开始注册控件权限映射
2025-07-28 18:32:39 [INFO] 开始初始化全局控件映射
2025-07-28 18:32:39 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-07-28 18:32:39 [DEBUG] 开始生成控件标题映射
2025-07-28 18:32:39 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-28 18:32:39 [DEBUG] 通过反射获取到 112 个字段
2025-07-28 18:32:39 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:40 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-28 18:32:40 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-28 18:32:40 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-28 18:32:40 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-28 18:32:40 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-28 18:32:40 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-28 18:32:40 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-28 18:32:40 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-07-28 18:32:40 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-07-28 18:32:40 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-07-28 18:32:40 [INFO] 控件标题映射生成完成，共生成 100 项映射
2025-07-28 18:32:40 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-28 18:32:40 [DEBUG] 全局控件标题映射生成完成，共生成 100 项
2025-07-28 18:32:40 [INFO] 关键控件标题映射: hyTab -> Develop
2025-07-28 18:32:40 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-07-28 18:32:40 [WARN] 关键控件未找到标题映射: buttonAbout
2025-07-28 18:32:40 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-07-28 18:32:40 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-07-28 18:32:40 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-07-28 18:32:40 [INFO] === znAbout控件标题映射诊断 ===
2025-07-28 18:32:40 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-07-28 18:32:40 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-07-28 18:32:40 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-07-28 18:32:40 [DEBUG] === 所有生成的控件标题映射 ===
2025-07-28 18:32:40 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-07-28 18:32:40 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-07-28 18:32:40 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-07-28 18:32:40 [DEBUG] 控件映射: btn发送及存档 -> '发送及存档'
2025-07-28 18:32:40 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-07-28 18:32:40 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-07-28 18:32:40 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-07-28 18:32:40 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-07-28 18:32:40 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-07-28 18:32:40 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规检查'
2025-07-28 18:32:40 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-07-28 18:32:40 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-07-28 18:32:40 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-07-28 18:32:40 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-07-28 18:32:40 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-07-28 18:32:40 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-07-28 18:32:40 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-07-28 18:32:40 [DEBUG] 控件映射: button14 -> '发送及存档'
2025-07-28 18:32:40 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-07-28 18:32:40 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-07-28 18:32:40 [DEBUG] 控件映射: button17 -> '向下填充'
2025-07-28 18:32:40 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-07-28 18:32:40 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-07-28 18:32:40 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-07-28 18:32:40 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-07-28 18:32:40 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-07-28 18:32:40 [DEBUG] 控件映射: button3 -> '关于'
2025-07-28 18:32:40 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-07-28 18:32:40 [DEBUG] 控件映射: button5 -> '文件管理'
2025-07-28 18:32:40 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-07-28 18:32:40 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-07-28 18:32:40 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-07-28 18:32:40 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-07-28 18:32:40 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-07-28 18:32:40 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-07-28 18:32:40 [DEBUG] 控件映射: button9 -> '文件快开'
2025-07-28 18:32:40 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-07-28 18:32:40 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-07-28 18:32:40 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-07-28 18:32:40 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-07-28 18:32:40 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-07-28 18:32:40 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-07-28 18:32:40 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-07-28 18:32:40 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-07-28 18:32:40 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-07-28 18:32:40 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-07-28 18:32:40 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-07-28 18:32:40 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-07-28 18:32:40 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-07-28 18:32:40 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-07-28 18:32:40 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-07-28 18:32:40 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-07-28 18:32:40 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-07-28 18:32:40 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-07-28 18:32:40 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-07-28 18:32:40 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-07-28 18:32:40 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-07-28 18:32:40 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-07-28 18:32:40 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-07-28 18:32:40 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-07-28 18:32:40 [DEBUG] 控件映射: button文件操作 -> '文件操作'
2025-07-28 18:32:40 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-07-28 18:32:40 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-07-28 18:32:40 [DEBUG] 控件映射: button专用工具 -> '专用工具'
2025-07-28 18:32:40 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-07-28 18:32:40 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-07-28 18:32:40 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-07-28 18:32:40 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-07-28 18:32:40 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-07-28 18:32:40 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-07-28 18:32:40 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-07-28 18:32:40 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-07-28 18:32:40 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-07-28 18:32:40 [DEBUG] 控件映射: group1 -> '关于'
2025-07-28 18:32:40 [DEBUG] 控件映射: group2 -> '脚本'
2025-07-28 18:32:40 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-07-28 18:32:40 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-07-28 18:32:40 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-07-28 18:32:40 [DEBUG] 控件映射: group文件 -> '文件'
2025-07-28 18:32:40 [DEBUG] 控件映射: group无线 -> '无线'
2025-07-28 18:32:40 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-07-28 18:32:40 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-07-28 18:32:40 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-07-28 18:32:40 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-07-28 18:32:40 [DEBUG] 控件映射: menu1 -> '其它'
2025-07-28 18:32:40 [DEBUG] 控件映射: menu3 -> '设置'
2025-07-28 18:32:40 [DEBUG] 控件映射: menu5 -> '修复'
2025-07-28 18:32:40 [DEBUG] 控件映射: menuHY -> '其它'
2025-07-28 18:32:40 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-07-28 18:32:40 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-07-28 18:32:40 [DEBUG] 控件映射: menu修复 -> '修复'
2025-07-28 18:32:40 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-07-28 18:32:40 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-07-28 18:32:40 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-07-28 18:32:40 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-07-28 18:32:40 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-07-28 18:32:40 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-07-28 18:32:40 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-07-28 18:32:40 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-07-28 18:32:40 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-07-28 18:32:40 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-07-28 18:32:40 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-07-28 18:32:40 [DEBUG] 开始生成控件权限映射
2025-07-28 18:32:40 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-28 18:32:40 [DEBUG] 通过反射获取到 112 个字段
2025-07-28 18:32:40 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-28 18:32:40 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-28 18:32:40 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-28 18:32:40 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-28 18:32:40 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-28 18:32:40 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-28 18:32:40 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-28 18:32:40 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-28 18:32:40 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-28 18:32:40 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-28 18:32:40 [INFO] 控件权限映射生成完成，共生成 104 项映射
2025-07-28 18:32:40 [DEBUG] 全局控件权限映射生成完成，共生成 104 项
2025-07-28 18:32:40 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-07-28 18:32:40 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-07-28 18:32:40 [INFO] 全局控件映射初始化完成 - 标题映射: 100 项, 权限映射: 104 项
2025-07-28 18:32:40 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-28 18:32:40 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-28 18:32:40 [INFO] 开始初始化权限验证
2025-07-28 18:32:40 [DEBUG] 设置默认UI可见性为false
2025-07-28 18:32:40 [DEBUG] 开始检查所有需要的权限
2025-07-28 18:32:40 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-28 18:32:40 [INFO] 启动网络授权信息获取任务
2025-07-28 18:32:40 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-07-28 18:32:40 [INFO] 所有权限检查完成
2025-07-28 18:32:40 [DEBUG] 应用权限状态到UI控件
2025-07-28 18:32:40 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 18:32:40 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 18:32:40 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:32:40 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 18:32:40 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 18:32:40 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:32:40 [DEBUG] 已应用权限状态到UI控件
2025-07-28 18:32:40 [DEBUG] 启动后台权限刷新任务
2025-07-28 18:32:40 [DEBUG] 启动延迟权限刷新任务
2025-07-28 18:32:40 [INFO] 权限验证初始化完成
2025-07-28 18:32:40 [INFO] UI权限管理初始化完成
2025-07-28 18:32:40 [INFO] 收到权限管理器初始化完成通知
2025-07-28 18:32:40 [INFO] 开始刷新控件标题
2025-07-28 18:32:40 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 18:32:40 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 18:32:40 [DEBUG] 控件标题刷新完成
2025-07-28 18:32:40 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 18:32:40 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 18:32:40 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 18:32:40 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:32:40 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 18:32:40 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 18:32:40 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 18:32:40 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 18:32:40 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 18:32:40 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 18:32:40 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 18:32:40 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:32:40 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 18:32:40 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 18:32:40 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 18:32:40 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 18:32:40 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 18:32:40 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 18:32:40 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 18:32:40 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 18:32:40 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 18:32:40 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 18:32:40 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 18:32:40 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 18:32:40 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 18:32:40 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 18:32:40 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 18:32:40 [INFO] 控件标题更正完成
2025-07-28 18:32:40 [INFO] 控件标题刷新完成
2025-07-28 18:32:40 [INFO] 权限管理器初始化完成处理结束
2025-07-28 18:32:40 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-28 18:32:40 [DEBUG] 授权验证初始化完成
2025-07-28 18:32:40 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-07-28 18:32:40 [INFO] 成功加载配置和授权信息
2025-07-28 18:32:40 [INFO] 开始初始化定时器和设置
2025-07-28 18:32:40 [INFO] 定时器和设置初始化完成
2025-07-28 18:32:40 [INFO] 开始VSTO插件启动流程
2025-07-28 18:32:40 [INFO] TopMostForm窗体加载完成
2025-07-28 18:32:41 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:32:41 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 2163514
2025-07-28 18:32:41 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 2163514)
2025-07-28 18:32:41 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 2163514
2025-07-28 18:32:41 [INFO] 系统事件监控已启动
2025-07-28 18:32:41 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:32:41 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-07-28 18:32:41 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-07-28 18:32:41 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-07-28 18:32:41 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 6101744
2025-07-28 18:32:41 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-07-28 18:32:41 [INFO] VSTO插件启动流程完成
2025-07-28 18:32:41 [DEBUG] 开始重置 208 个命令栏
2025-07-28 18:32:41 [INFO] 从Remote成功获取到网络授权信息
2025-07-28 18:32:41 [INFO] 网络授权信息已更新并触发回调
2025-07-28 18:32:41 [INFO] 网络授权信息已从 Network 更新
2025-07-28 18:32:41 [INFO] 授权版本: 1.0
2025-07-28 18:32:41 [INFO] 颁发者: ExtensionsTools
2025-07-28 18:32:41 [INFO] 用户数量: 2
2025-07-28 18:32:41 [INFO] 分组权限数量: 2
2025-07-28 18:32:41 [WARN] 配置文件中未找到用户组信息
2025-07-28 18:32:41 [INFO] 已重新设置用户组: []
2025-07-28 18:32:41 [INFO] 用户组信息已重新设置
2025-07-28 18:32:41 [INFO] 立即刷新权限缓存和UI界面
2025-07-28 18:32:41 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-28 18:32:41 [DEBUG] 使用新的权限管理器进行强制刷新
2025-07-28 18:32:41 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-07-28 18:32:41 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-28 18:32:41 [DEBUG] 本地权限缓存已清空
2025-07-28 18:32:41 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-07-28 18:32:41 [DEBUG] 重置命令栏: cell
2025-07-28 18:32:41 [INFO] 所有权限检查完成
2025-07-28 18:32:41 [DEBUG] 权限重新检查完成
2025-07-28 18:32:41 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 18:32:41 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 18:32:41 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:32:41 [DEBUG] 重置命令栏: column
2025-07-28 18:32:41 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 18:32:41 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 18:32:41 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:32:41 [DEBUG] 已应用权限状态到UI控件
2025-07-28 18:32:41 [INFO] UI界面权限状态已更新
2025-07-28 18:32:41 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 18:32:41 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 18:32:41 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-07-28 18:32:41 [INFO] 权限缓存和UI界面立即刷新完成
2025-07-28 18:32:41 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 18:32:41 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 18:32:41 [INFO] 网络授权已更新，开始刷新控件标题
2025-07-28 18:32:41 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:32:42 [INFO] 开始刷新Ribbon控件标题
2025-07-28 18:32:42 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 18:32:42 [DEBUG] 权限缓存已清空，清除了 104 个缓存项
2025-07-28 18:32:42 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 18:32:42 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:32:42 [DEBUG] 已应用权限状态到UI控件
2025-07-28 18:32:42 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 18:32:42 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 18:32:42 [DEBUG] 重置命令栏: row
2025-07-28 18:32:42 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 18:32:42 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:32:42 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 18:32:42 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 18:32:42 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 18:32:42 [DEBUG] 重置命令栏: cell
2025-07-28 18:32:42 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 18:32:42 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 18:32:42 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 18:32:42 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 18:32:42 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:32:42 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 18:32:42 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 18:32:42 [DEBUG] 重置命令栏: column
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 18:32:42 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 18:32:42 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 18:32:42 [DEBUG] 重置命令栏: row
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 18:32:42 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 18:32:42 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 18:32:42 [INFO] 控件标题更正完成
2025-07-28 18:32:42 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-07-28 18:32:42 [INFO] Ribbon控件标题刷新完成
2025-07-28 18:32:42 [INFO] 控件标题刷新完成
2025-07-28 18:32:42 [DEBUG] Ribbon控件标题已刷新
2025-07-28 18:32:42 [INFO] 开始刷新控件标题
2025-07-28 18:32:42 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 18:32:42 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 18:32:42 [DEBUG] 控件标题刷新完成
2025-07-28 18:32:42 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 18:32:42 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 18:32:42 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 18:32:42 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:32:42 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 18:32:42 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 18:32:42 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 18:32:42 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 18:32:42 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 18:32:42 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 18:32:42 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 18:32:42 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:32:42 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 18:32:42 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 18:32:42 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 18:32:42 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 18:32:42 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 18:32:42 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 18:32:42 [INFO] 控件标题更正完成
2025-07-28 18:32:42 [INFO] 控件标题刷新完成
2025-07-28 18:32:42 [DEBUG] Ribbon控件标题已立即刷新
2025-07-28 18:32:42 [DEBUG] 重置命令栏: row
2025-07-28 18:32:42 [DEBUG] 重置命令栏: column
2025-07-28 18:32:42 [INFO] 开始刷新授权状态
2025-07-28 18:32:42 [DEBUG] 开始初始化授权验证
2025-07-28 18:32:42 [DEBUG] 使用新的权限管理器进行初始化
2025-07-28 18:32:42 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-28 18:32:42 [INFO] 开始初始化UI权限管理
2025-07-28 18:32:42 [DEBUG] [实例ID: 67dbb1ac] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-28 18:32:42 [DEBUG] 🔍 [实例ID: 67dbb1ac] 字典引用一致性检查:
2025-07-28 18:32:42 [DEBUG] 🔍   标题映射一致性: True
2025-07-28 18:32:42 [DEBUG] 🔍   权限映射一致性: True
2025-07-28 18:32:42 [DEBUG] 🔍   信息映射一致性: True
2025-07-28 18:32:42 [DEBUG] 🔍   特殊控件一致性: True
2025-07-28 18:32:42 [DEBUG] 控件权限管理器初始化完成 [实例ID: 67dbb1ac]
2025-07-28 18:32:42 [DEBUG] 开始注册控件权限映射
2025-07-28 18:32:42 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-28 18:32:42 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-28 18:32:42 [INFO] 开始初始化权限验证
2025-07-28 18:32:42 [DEBUG] 设置默认UI可见性为false
2025-07-28 18:32:42 [DEBUG] 开始检查所有需要的权限
2025-07-28 18:32:42 [INFO] 所有权限检查完成
2025-07-28 18:32:42 [DEBUG] 应用权限状态到UI控件
2025-07-28 18:32:42 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 18:32:42 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:32:42 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 18:32:42 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:32:42 [DEBUG] 已应用权限状态到UI控件
2025-07-28 18:32:42 [DEBUG] 启动后台权限刷新任务
2025-07-28 18:32:42 [DEBUG] 启动延迟权限刷新任务
2025-07-28 18:32:42 [INFO] 权限验证初始化完成
2025-07-28 18:32:42 [INFO] UI权限管理初始化完成
2025-07-28 18:32:42 [INFO] 收到权限管理器初始化完成通知
2025-07-28 18:32:42 [INFO] 开始刷新控件标题
2025-07-28 18:32:42 [DEBUG] 开始刷新所有控件权限状态
2025-07-28 18:32:42 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-28 18:32:42 [DEBUG] 控件标题刷新完成
2025-07-28 18:32:42 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-28 18:32:42 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-28 18:32:42 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-28 18:32:42 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:32:42 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-28 18:32:42 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-28 18:32:42 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 18:32:42 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-28 18:32:42 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 18:32:42 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:32:42 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-28 18:32:42 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 18:32:42 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 18:32:42 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:32:42 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-28 18:32:42 [DEBUG] 已应用权限状态到UI控件
2025-07-28 18:32:42 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-28 18:32:42 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-28 18:32:42 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-28 18:32:42 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-28 18:32:42 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-28 18:32:42 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-28 18:32:42 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-28 18:32:42 [INFO] 控件标题更正完成
2025-07-28 18:32:42 [INFO] 控件标题刷新完成
2025-07-28 18:32:42 [INFO] 权限管理器初始化完成处理结束
2025-07-28 18:32:42 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-28 18:32:42 [DEBUG] 授权验证初始化完成
2025-07-28 18:32:42 [INFO] 授权状态刷新完成
2025-07-28 18:32:42 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 18:32:42 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:32:42 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 18:32:42 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 18:32:42 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:32:42 [DEBUG] 已应用权限状态到UI控件
2025-07-28 18:32:43 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:32:43 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 2163514
2025-07-28 18:32:43 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 2163514)
2025-07-28 18:32:43 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-28 18:32:43 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-28 18:32:43 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-07-28 18:32:44 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-28 18:32:44 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-28 18:32:44 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:32:44 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-28 18:32:44 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-28 18:32:44 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-28 18:32:44 [DEBUG] 已应用权限状态到UI控件
2025-07-28 18:32:45 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 18:32:45 [DEBUG] 授权控制器已初始化
2025-07-28 18:32:45 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 18:32:46 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 18:32:46 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 18:32:46 [DEBUG] 授权控制器已初始化
2025-07-28 18:32:46 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 18:32:47 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 18:32:47 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 18:32:47 [DEBUG] 授权控制器已初始化
2025-07-28 18:32:47 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 18:32:47 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 18:32:48 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 18:32:48 [DEBUG] 授权控制器已初始化
2025-07-28 18:32:48 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 18:32:48 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 18:32:49 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 18:32:49 [DEBUG] 授权控制器已初始化
2025-07-28 18:32:49 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 18:32:49 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 18:32:49 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 18:32:49 [DEBUG] 授权控制器已初始化
2025-07-28 18:32:49 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 18:32:49 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 18:32:50 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 18:32:50 [DEBUG] 授权控制器已初始化
2025-07-28 18:32:50 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 18:32:50 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 18:32:50 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 18:32:50 [DEBUG] 授权控制器已初始化
2025-07-28 18:32:50 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-28 18:32:50 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-28 18:32:51 [DEBUG] 已重置工作表标签菜单
2025-07-28 18:32:51 [DEBUG] 工作表标签菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-28 18:32:55 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 18:32:55 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 18:32:55 [INFO] OpenForm: 准备打开窗体 '合规检查'，位置: Right，单实例: True
2025-07-28 18:32:55 [INFO] 开始显示窗体 '合规检查'，位置模式: Right
2025-07-28 18:32:55 [INFO] 窗体 '合规检查' 以TopMostForm为父窗体显示
2025-07-28 18:32:55 [INFO] 窗体 '合规检查' 显示完成，句柄: 48241208
2025-07-28 18:32:55 [INFO] OpenForm: 窗体 '合规检查' 打开成功
2025-07-28 18:32:59 [INFO] OpenForm: 准备打开窗体 '备份及发送'，位置: Center，单实例: True
2025-07-28 18:32:59 [INFO] 开始显示窗体 '备份及发送'，位置模式: Center
2025-07-28 18:32:59 [INFO] 窗体 '备份及发送' 以TopMostForm为父窗体显示
2025-07-28 18:32:59 [INFO] 窗体 '备份及发送' 显示完成，句柄: 4392166
2025-07-28 18:32:59 [INFO] OpenForm: 窗体 '备份及发送' 打开成功
2025-07-28 18:33:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-28 18:33:17 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-28 18:33:17 [INFO] OpenForm: 准备打开窗体 '合规检查'，位置: Right，单实例: True
2025-07-28 18:33:17 [INFO] 开始显示窗体 '合规检查'，位置模式: Right
2025-07-28 18:33:17 [INFO] 窗体 '合规检查' 以TopMostForm为父窗体显示
2025-07-28 18:33:17 [INFO] 窗体 '合规检查' 显示完成，句柄: 5574034
2025-07-28 18:33:17 [INFO] OpenForm: 窗体 '合规检查' 打开成功
2025-07-28 18:33:21 [INFO] OpenForm: 准备打开窗体 'ExcelFileManager'，位置: Center，单实例: True
2025-07-28 18:33:21 [INFO] 开始显示窗体 'ExcelFileManager'，位置模式: Center
2025-07-28 18:33:21 [INFO] 窗体 'ExcelFileManager' 以TopMostForm为父窗体显示
2025-07-28 18:33:21 [INFO] 窗体 'ExcelFileManager' 显示完成，句柄: 7148284
2025-07-28 18:33:21 [INFO] OpenForm: 窗体 'ExcelFileManager' 打开成功
2025-07-28 18:33:24 [INFO] OpenForm: 准备打开窗体 '备份及发送'，位置: Center，单实例: True
2025-07-28 18:33:24 [INFO] 开始显示窗体 '备份及发送'，位置模式: Center
2025-07-28 18:33:24 [INFO] 窗体 '备份及发送' 以TopMostForm为父窗体显示
2025-07-28 18:33:24 [INFO] 窗体 '备份及发送' 显示完成，句柄: 24777078
2025-07-28 18:33:24 [INFO] OpenForm: 窗体 '备份及发送' 打开成功
2025-07-28 18:33:32 [ERROR] Excel
异常详情：System.Runtime.InteropServices.COMException (0x800A03EC): 异常来自 HRESULT:0x800A03EC
   在 Microsoft.Office.Interop.Excel._Workbook.SaveCopyAs(Object Filename)
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 379
2025-07-28 18:33:32 [ERROR] 文件操作
异常详情：ET.ETException: 保存文件副本时出错: 异常来自 HRESULT:0x800A03EC ---> System.Runtime.InteropServices.COMException: 异常来自 HRESULT:0x800A03EC
   在 Microsoft.Office.Interop.Excel._Workbook.SaveCopyAs(Object Filename)
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 379
   --- 内部异常堆栈跟踪的结尾 ---
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 385
   在 HyExcelVsto.Module.Common.frm备份及发送.生成发送存档(String fileName, Boolean includeDate, Boolean includeTime, String recipient, String description, String backupFolder) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 180
2025-07-28 18:33:32 [ERROR] 文件操作
异常详情：ET.ETException: 生成存档文件失败 ---> ET.ETException: 保存文件副本时出错: 异常来自 HRESULT:0x800A03EC ---> System.Runtime.InteropServices.COMException: 异常来自 HRESULT:0x800A03EC
   在 Microsoft.Office.Interop.Excel._Workbook.SaveCopyAs(Object Filename)
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 379
   --- 内部异常堆栈跟踪的结尾 ---
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 385
   在 HyExcelVsto.Module.Common.frm备份及发送.生成发送存档(String fileName, Boolean includeDate, Boolean includeTime, String recipient, String description, String backupFolder) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 180
   --- 内部异常堆栈跟踪的结尾 ---
   在 HyExcelVsto.Module.Common.frm备份及发送.生成发送存档(String fileName, Boolean includeDate, Boolean includeTime, String recipient, String description, String backupFolder) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 199
   在 HyExcelVsto.Module.Common.frm备份及发送.button生成发送存档_Click(Object sender, EventArgs e) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 71
2025-07-28 18:34:02 [ERROR] Excel
异常详情：System.Runtime.InteropServices.COMException (0x800A03EC): 生成发送存档失败
   在 Microsoft.Office.Interop.Excel._Workbook.SaveCopyAs(Object Filename)
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 379
2025-07-28 18:34:02 [ERROR] 文件操作
异常详情：ET.ETException: 保存文件副本时出错: 生成发送存档失败 ---> System.Runtime.InteropServices.COMException: 生成发送存档失败
   在 Microsoft.Office.Interop.Excel._Workbook.SaveCopyAs(Object Filename)
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 379
   --- 内部异常堆栈跟踪的结尾 ---
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 385
   在 HyExcelVsto.Module.Common.frm备份及发送.生成发送存档(String fileName, Boolean includeDate, Boolean includeTime, String recipient, String description, String backupFolder) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 180
2025-07-28 18:34:17 [ERROR] 文件操作
异常详情：ET.ETException: 生成存档文件失败 ---> ET.ETException: 保存文件副本时出错: 生成发送存档失败 ---> System.Runtime.InteropServices.COMException: 生成发送存档失败
   在 Microsoft.Office.Interop.Excel._Workbook.SaveCopyAs(Object Filename)
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 379
   --- 内部异常堆栈跟踪的结尾 ---
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 385
   在 HyExcelVsto.Module.Common.frm备份及发送.生成发送存档(String fileName, Boolean includeDate, Boolean includeTime, String recipient, String description, String backupFolder) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 180
   --- 内部异常堆栈跟踪的结尾 ---
   在 HyExcelVsto.Module.Common.frm备份及发送.生成发送存档(String fileName, Boolean includeDate, Boolean includeTime, String recipient, String description, String backupFolder) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 199
   在 HyExcelVsto.Module.Common.frm备份及发送.button生成发送存档_Click(Object sender, EventArgs e) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 71
2025-07-28 18:34:35 [ERROR] Excel
异常详情：System.Runtime.InteropServices.COMException (0x800A03EC): 生成发送存档失败
   在 Microsoft.Office.Interop.Excel._Workbook.SaveCopyAs(Object Filename)
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 379
2025-07-28 18:34:42 [ERROR] 文件操作
异常详情：ET.ETException: 保存文件副本时出错: 生成发送存档失败 ---> System.Runtime.InteropServices.COMException: 生成发送存档失败
   在 Microsoft.Office.Interop.Excel._Workbook.SaveCopyAs(Object Filename)
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 379
   --- 内部异常堆栈跟踪的结尾 ---
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 385
   在 HyExcelVsto.Module.Common.frm备份及发送.生成发送存档(String fileName, Boolean includeDate, Boolean includeTime, String recipient, String description, String backupFolder) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 180
2025-07-28 18:34:44 [ERROR] 文件操作
异常详情：ET.ETException: 生成存档文件失败 ---> ET.ETException: 保存文件副本时出错: 生成发送存档失败 ---> System.Runtime.InteropServices.COMException: 生成发送存档失败
   在 Microsoft.Office.Interop.Excel._Workbook.SaveCopyAs(Object Filename)
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 379
   --- 内部异常堆栈跟踪的结尾 ---
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 385
   在 HyExcelVsto.Module.Common.frm备份及发送.生成发送存档(String fileName, Boolean includeDate, Boolean includeTime, String recipient, String description, String backupFolder) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 180
   --- 内部异常堆栈跟踪的结尾 ---
   在 HyExcelVsto.Module.Common.frm备份及发送.生成发送存档(String fileName, Boolean includeDate, Boolean includeTime, String recipient, String description, String backupFolder) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 199
   在 HyExcelVsto.Module.Common.frm备份及发送.button生成发送存档_Click(Object sender, EventArgs e) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 71
2025-07-28 18:35:32 [ERROR] Excel
异常详情：System.Runtime.InteropServices.COMException (0x800A03EC): 生成发送存档失败
   在 Microsoft.Office.Interop.Excel._Workbook.SaveCopyAs(Object Filename)
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 379
2025-07-28 18:35:32 [ERROR] 文件操作
异常详情：ET.ETException: 保存文件副本时出错: 生成发送存档失败 ---> System.Runtime.InteropServices.COMException: 生成发送存档失败
   在 Microsoft.Office.Interop.Excel._Workbook.SaveCopyAs(Object Filename)
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 379
   --- 内部异常堆栈跟踪的结尾 ---
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 385
   在 HyExcelVsto.Module.Common.frm备份及发送.生成发送存档(String fileName, Boolean includeDate, Boolean includeTime, String recipient, String description, String backupFolder) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 180
2025-07-28 18:35:40 [ERROR] 文件操作
异常详情：ET.ETException: 生成存档文件失败 ---> ET.ETException: 保存文件副本时出错: 生成发送存档失败 ---> System.Runtime.InteropServices.COMException: 生成发送存档失败
   在 Microsoft.Office.Interop.Excel._Workbook.SaveCopyAs(Object Filename)
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 379
   --- 内部异常堆栈跟踪的结尾 ---
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 385
   在 HyExcelVsto.Module.Common.frm备份及发送.生成发送存档(String fileName, Boolean includeDate, Boolean includeTime, String recipient, String description, String backupFolder) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 180
   --- 内部异常堆栈跟踪的结尾 ---
   在 HyExcelVsto.Module.Common.frm备份及发送.生成发送存档(String fileName, Boolean includeDate, Boolean includeTime, String recipient, String description, String backupFolder) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 199
   在 HyExcelVsto.Module.Common.frm备份及发送.button生成发送存档_Click(Object sender, EventArgs e) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 71
2025-07-28 18:37:17 [ERROR] Excel
异常详情：System.Runtime.InteropServices.COMException (0x800A03EC): 生成发送存档失败
   在 Microsoft.Office.Interop.Excel._Workbook.SaveCopyAs(Object Filename)
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 379
2025-07-28 18:37:17 [ERROR] 文件操作
异常详情：ET.ETException: 保存文件副本时出错: 生成发送存档失败 ---> System.Runtime.InteropServices.COMException: 生成发送存档失败
   在 Microsoft.Office.Interop.Excel._Workbook.SaveCopyAs(Object Filename)
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 379
   --- 内部异常堆栈跟踪的结尾 ---
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 385
   在 HyExcelVsto.Module.Common.frm备份及发送.生成发送存档(String fileName, Boolean includeDate, Boolean includeTime, String recipient, String description, String backupFolder) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 180
2025-07-28 18:37:17 [ERROR] 文件操作
异常详情：ET.ETException: 生成存档文件失败 ---> ET.ETException: 保存文件副本时出错: 生成发送存档失败 ---> System.Runtime.InteropServices.COMException: 生成发送存档失败
   在 Microsoft.Office.Interop.Excel._Workbook.SaveCopyAs(Object Filename)
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 379
   --- 内部异常堆栈跟踪的结尾 ---
   在 ET.ETExcelExtensions.SaveCopyAs(Workbook workbook, String filePath) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs:行号 385
   在 HyExcelVsto.Module.Common.frm备份及发送.生成发送存档(String fileName, Boolean includeDate, Boolean includeTime, String recipient, String description, String backupFolder) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 180
   --- 内部异常堆栈跟踪的结尾 ---
   在 HyExcelVsto.Module.Common.frm备份及发送.生成发送存档(String fileName, Boolean includeDate, Boolean includeTime, String recipient, String description, String backupFolder) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 199
   在 HyExcelVsto.Module.Common.frm备份及发送.button生成发送存档_Click(Object sender, EventArgs e) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs:行号 71
2025-07-28 18:37:30 [INFO] App_WorkbookOpen: 工作簿 '2025年无线网5GA工程管控表0727.xlsx' 打开事件触发
2025-07-28 18:37:30 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:37:30 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 2163514, 新父窗口: 7542988
2025-07-28 18:37:30 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7542988)
2025-07-28 18:37:30 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:37:30 [INFO] App_WorkbookOpen: 工作簿 '2025年无线网5GA工程管控表0727.xlsx' 打开处理完成
2025-07-28 18:37:30 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:37:30 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7542988)
2025-07-28 18:37:30 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:37:30 [INFO] App_WorkbookActivate: 工作簿 '2025年无线网5GA工程管控表0727.xlsx' 激活处理完成
2025-07-28 18:37:30 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-28 18:37:30 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7542988)
2025-07-28 18:37:30 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-28 18:37:30 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-28 18:37:31 [WARN] 检测到Excel窗口句柄变化: 2163514 -> 7542988
2025-07-28 18:37:31 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7542988)
2025-07-28 18:37:31 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:37:31 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-28 18:37:31 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-28 18:37:31 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:37:31 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-28 18:37:31 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-28 18:37:31 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-28 18:37:31 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-28 18:37:32 [INFO] OpenForm: 准备打开窗体 '备份及发送'，位置: Center，单实例: True
2025-07-28 18:37:32 [INFO] 开始显示窗体 '备份及发送'，位置模式: Center
2025-07-28 18:37:32 [INFO] 窗体 '备份及发送' 以TopMostForm为父窗体显示
2025-07-28 18:37:32 [INFO] 窗体 '备份及发送' 显示完成，句柄: 6822612
2025-07-28 18:37:32 [INFO] OpenForm: 窗体 '备份及发送' 打开成功
2025-07-28 18:37:39 [INFO] 成功生成发送存档：E:\资料存放\OfficeAutoBackup\发送存档\2025年\测试\20250728-183734-2025年无线网5GA工程管控表0727\2025年无线网5GA工程管控表0727-20250728-183734.xlsx
2025-07-28 18:37:48 [INFO] 成功生成发送存档：E:\资料存放\OfficeAutoBackup\发送存档\2025年\测试2\20250728-183748-2025年无线网5GA工程管控表0727\2025年无线网5GA工程管控表0727-20250728-183748.xlsx
2025-07-28 18:37:56 [INFO] OpenForm: 准备打开窗体 '备份及发送'，位置: Center，单实例: True
2025-07-28 18:37:56 [INFO] 开始显示窗体 '备份及发送'，位置模式: Center
2025-07-28 18:37:56 [INFO] 窗体 '备份及发送' 以TopMostForm为父窗体显示
2025-07-28 18:37:56 [INFO] 窗体 '备份及发送' 显示完成，句柄: 2627904
2025-07-28 18:37:56 [INFO] OpenForm: 窗体 '备份及发送' 打开成功
